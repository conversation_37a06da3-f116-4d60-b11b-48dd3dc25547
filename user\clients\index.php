<?php
/**
 * صفحة إدارة العملاء
 * Clients Management Page
 */

require_once '../../config/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../../login.php');
}

$user = getCurrentUser();
$company_id = $user['company_id'];

// معالجة البحث والفلترة
$search = sanitize($_GET['search'] ?? '');
$type_filter = sanitize($_GET['type'] ?? '');
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = RECORDS_PER_PAGE;
$offset = ($page - 1) * $per_page;

// بناء استعلام البحث
$where_conditions = ['company_id = ?'];
$params = [$company_id];

if (!empty($search)) {
    $where_conditions[] = '(name LIKE ? OR email LIKE ? OR phone LIKE ? OR national_id LIKE ?)';
    $search_term = "%{$search}%";
    $params = array_merge($params, [$search_term, $search_term, $search_term, $search_term]);
}

if (!empty($type_filter)) {
    $where_conditions[] = 'client_type = ?';
    $params[] = $type_filter;
}

$where_clause = implode(' AND ', $where_conditions);

try {
    // عد إجمالي العملاء
    $total_clients = $database->count('clients', $where_clause, $params);
    
    // جلب العملاء مع الترقيم
    $sql = "SELECT c.*, u.full_name as agent_name,
                   (SELECT COUNT(*) FROM transactions WHERE client_id = c.id) as transactions_count,
                   (SELECT SUM(amount) FROM transactions WHERE client_id = c.id AND status = 'completed') as total_transactions
            FROM clients c 
            LEFT JOIN users u ON c.user_id = u.id 
            WHERE {$where_clause} 
            ORDER BY c.created_at DESC 
            LIMIT {$per_page} OFFSET {$offset}";
    
    $clients = $database->fetchAll($sql, $params);
    
    // حساب عدد الصفحات
    $total_pages = ceil($total_clients / $per_page);
    
    // إحصائيات العملاء
    $stats = [];
    foreach (CLIENT_TYPES as $key => $value) {
        $stats[$key] = $database->count('clients', 'company_id = ? AND client_type = ?', [$company_id, $key]);
    }
    
} catch (Exception $e) {
    $error_message = "حدث خطأ في جلب البيانات";
    $clients = [];
    $total_clients = 0;
    $total_pages = 0;
    $stats = [];
}

// معالجة حذف العميل
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_client'])) {
    $client_id = intval($_POST['client_id']);
    
    try {
        // التحقق من ملكية العميل
        $client = $database->fetch("SELECT * FROM clients WHERE id = ? AND company_id = ?", [$client_id, $company_id]);
        
        if ($client) {
            // التحقق من وجود معاملات مرتبطة
            $transactions_count = $database->count('transactions', 'client_id = ?', [$client_id]);
            
            if ($transactions_count > 0) {
                setAlert('لا يمكن حذف العميل لوجود معاملات مرتبطة به', 'error');
            } else {
                $database->delete('clients', 'id = ?', [$client_id]);
                setAlert('تم حذف العميل بنجاح', 'success');
            }
        } else {
            setAlert('العميل غير موجود', 'error');
        }
    } catch (Exception $e) {
        setAlert('حدث خطأ أثناء حذف العميل', 'error');
    }
    
    redirect('index.php');
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العملاء - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <style>
        .client-card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }
        
        .client-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        
        .client-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
            margin-left: 1rem;
        }
        
        .client-type-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
        }
        
        .stats-summary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .search-box {
            position: relative;
        }
        
        .search-box .form-control {
            padding-right: 3rem;
        }
        
        .search-box .search-icon {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }
        
        .client-info h6 {
            margin: 0;
            font-weight: 600;
            color: #333;
        }
        
        .client-info small {
            color: #666;
        }
        
        .client-stats {
            font-size: 0.9rem;
            color: #666;
        }
        
        .action-buttons .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2>إدارة العملاء</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="../dashboard.php">لوحة التحكم</a></li>
                                <li class="breadcrumb-item active">العملاء</li>
                            </ol>
                        </nav>
                    </div>
                    <a href="add.php" class="btn btn-primary">
                        <i class="bi bi-person-plus me-2"></i>
                        إضافة عميل جديد
                    </a>
                </div>
                
                <!-- Alerts -->
                <?php showAlert(); ?>
                
                <!-- Stats Summary -->
                <div class="stats-summary">
                    <div class="row text-center">
                        <div class="col-md-2">
                            <h3><?php echo formatNumber($total_clients); ?></h3>
                            <small>إجمالي العملاء</small>
                        </div>
                        <?php foreach (CLIENT_TYPES as $key => $value): ?>
                            <div class="col-md-2">
                                <h3><?php echo formatNumber($stats[$key] ?? 0); ?></h3>
                                <small><?php echo $value; ?></small>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- Filters -->
                <div class="filter-section">
                    <form method="GET" class="row g-3">
                        <div class="col-md-6">
                            <div class="search-box">
                                <input type="text" class="form-control" name="search" 
                                       placeholder="البحث في العملاء (الاسم، البريد، الهاتف، الهوية)..." 
                                       value="<?php echo htmlspecialchars($search); ?>">
                                <i class="bi bi-search search-icon"></i>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <select name="type" class="form-select">
                                <option value="">جميع أنواع العملاء</option>
                                <?php foreach (CLIENT_TYPES as $key => $value): ?>
                                    <option value="<?php echo $key; ?>" <?php echo $type_filter === $key ? 'selected' : ''; ?>>
                                        <?php echo $value; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="bi bi-funnel me-2"></i>
                                فلترة
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- Clients List -->
                <?php if (empty($clients)): ?>
                    <div class="text-center py-5">
                        <i class="bi bi-people display-1 text-muted"></i>
                        <h4 class="mt-3">لا يوجد عملاء</h4>
                        <p class="text-muted">لم يتم العثور على عملاء يطابقون معايير البحث</p>
                        <a href="add.php" class="btn btn-primary">إضافة عميل جديد</a>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($clients as $client): ?>
                            <div class="col-lg-6 col-xl-4">
                                <div class="card client-card h-100">
                                    <div class="card-body">
                                        <div class="d-flex align-items-start mb-3">
                                            <div class="client-avatar">
                                                <?php echo strtoupper(substr($client['name'], 0, 2)); ?>
                                            </div>
                                            <div class="client-info flex-grow-1">
                                                <h6><?php echo htmlspecialchars($client['name']); ?></h6>
                                                <small class="text-muted">
                                                    <i class="bi bi-telephone me-1"></i>
                                                    <?php echo htmlspecialchars($client['phone']); ?>
                                                </small>
                                                <?php if ($client['email']): ?>
                                                    <br>
                                                    <small class="text-muted">
                                                        <i class="bi bi-envelope me-1"></i>
                                                        <?php echo htmlspecialchars($client['email']); ?>
                                                    </small>
                                                <?php endif; ?>
                                            </div>
                                            <span class="client-type-badge badge bg-<?php 
                                                echo match($client['client_type']) {
                                                    'buyer' => 'success',
                                                    'seller' => 'primary',
                                                    'renter' => 'info',
                                                    'landlord' => 'warning',
                                                    default => 'secondary'
                                                };
                                            ?>">
                                                <?php echo CLIENT_TYPES[$client['client_type']]; ?>
                                            </span>
                                        </div>
                                        
                                        <?php if ($client['national_id']): ?>
                                            <div class="mb-2">
                                                <small class="text-muted">
                                                    <i class="bi bi-card-text me-1"></i>
                                                    رقم الهوية: <?php echo htmlspecialchars($client['national_id']); ?>
                                                </small>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if ($client['address']): ?>
                                            <div class="mb-2">
                                                <small class="text-muted">
                                                    <i class="bi bi-geo-alt me-1"></i>
                                                    <?php echo htmlspecialchars($client['address']); ?>
                                                </small>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="client-stats mb-3">
                                            <div class="row text-center">
                                                <div class="col-6">
                                                    <strong><?php echo $client['transactions_count']; ?></strong>
                                                    <br>
                                                    <small>معاملة</small>
                                                </div>
                                                <div class="col-6">
                                                    <strong><?php echo formatCurrency($client['total_transactions'] ?? 0); ?></strong>
                                                    <br>
                                                    <small>إجمالي المعاملات</small>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <?php if ($client['agent_name']): ?>
                                            <div class="mb-2">
                                                <small class="text-muted">
                                                    <i class="bi bi-person-badge me-1"></i>
                                                    الوسيط: <?php echo htmlspecialchars($client['agent_name']); ?>
                                                </small>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="action-buttons">
                                            <div class="btn-group w-100" role="group">
                                                <a href="view.php?id=<?php echo $client['id']; ?>" 
                                                   class="btn btn-outline-primary btn-sm">
                                                    <i class="bi bi-eye"></i>
                                                    عرض
                                                </a>
                                                <a href="edit.php?id=<?php echo $client['id']; ?>" 
                                                   class="btn btn-outline-secondary btn-sm">
                                                    <i class="bi bi-pencil"></i>
                                                    تعديل
                                                </a>
                                                <a href="../transactions/add.php?client_id=<?php echo $client['id']; ?>" 
                                                   class="btn btn-outline-success btn-sm">
                                                    <i class="bi bi-plus"></i>
                                                    معاملة
                                                </a>
                                                <button type="button" 
                                                        class="btn btn-outline-danger btn-sm"
                                                        onclick="deleteClient(<?php echo $client['id']; ?>, '<?php echo htmlspecialchars($client['name']); ?>', <?php echo $client['transactions_count']; ?>)">
                                                    <i class="bi bi-trash"></i>
                                                    حذف
                                                </button>
                                            </div>
                                        </div>
                                        
                                        <div class="text-muted small mt-2 text-center">
                                            <i class="bi bi-calendar me-1"></i>
                                            انضم في <?php echo formatDate($client['created_at'], 'arabic_date'); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                        <nav aria-label="تنقل الصفحات" class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page - 1; ?>&<?php echo http_build_query(array_filter($_GET, fn($key) => $key !== 'page', ARRAY_FILTER_USE_KEY)); ?>">
                                            السابق
                                        </a>
                                    </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>&<?php echo http_build_query(array_filter($_GET, fn($key) => $key !== 'page', ARRAY_FILTER_USE_KEY)); ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>
                                
                                <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page + 1; ?>&<?php echo http_build_query(array_filter($_GET, fn($key) => $key !== 'page', ARRAY_FILTER_USE_KEY)); ?>">
                                            التالي
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف العميل "<span id="clientName"></span>"؟</p>
                    <div id="transactionsWarning" class="alert alert-warning" style="display: none;">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        هذا العميل لديه <span id="transactionsCount"></span> معاملة. لا يمكن حذفه.
                    </div>
                    <p class="text-danger small" id="deleteWarning">هذا الإجراء لا يمكن التراجع عنه.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form method="POST" style="display: inline;" id="deleteForm">
                        <input type="hidden" name="client_id" id="deleteClientId">
                        <button type="submit" name="delete_client" class="btn btn-danger" id="confirmDeleteBtn">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function deleteClient(id, name, transactionsCount) {
            document.getElementById('deleteClientId').value = id;
            document.getElementById('clientName').textContent = name;
            document.getElementById('transactionsCount').textContent = transactionsCount;
            
            const warningDiv = document.getElementById('transactionsWarning');
            const deleteWarning = document.getElementById('deleteWarning');
            const confirmBtn = document.getElementById('confirmDeleteBtn');
            
            if (transactionsCount > 0) {
                warningDiv.style.display = 'block';
                deleteWarning.style.display = 'none';
                confirmBtn.style.display = 'none';
            } else {
                warningDiv.style.display = 'none';
                deleteWarning.style.display = 'block';
                confirmBtn.style.display = 'inline-block';
            }
            
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
        
        // تحسين تجربة البحث
        let searchTimeout;
        document.querySelector('input[name="search"]').addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.form.submit();
            }, 500);
        });
        
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.client-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 50);
            });
        });
    </script>
</body>
</html>
