<?php
/**
 * صفحة إدارة المعاملات
 * Transactions Management Page
 */

require_once '../../config/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../../login.php');
}

$user = getCurrentUser();
$company_id = $user['company_id'];

// معالجة البحث والفلترة
$search = sanitize($_GET['search'] ?? '');
$status_filter = sanitize($_GET['status'] ?? '');
$type_filter = sanitize($_GET['type'] ?? '');
$date_from = sanitize($_GET['date_from'] ?? '');
$date_to = sanitize($_GET['date_to'] ?? '');
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = RECORDS_PER_PAGE;
$offset = ($page - 1) * $per_page;

// بناء استعلام البحث
$where_conditions = ['t.company_id = ?'];
$params = [$company_id];

if (!empty($search)) {
    $where_conditions[] = '(p.title LIKE ? OR c.name LIKE ? OR t.notes LIKE ?)';
    $search_term = "%{$search}%";
    $params = array_merge($params, [$search_term, $search_term, $search_term]);
}

if (!empty($status_filter)) {
    $where_conditions[] = 't.status = ?';
    $params[] = $status_filter;
}

if (!empty($type_filter)) {
    $where_conditions[] = 't.transaction_type = ?';
    $params[] = $type_filter;
}

if (!empty($date_from)) {
    $where_conditions[] = 'DATE(t.created_at) >= ?';
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = 'DATE(t.created_at) <= ?';
    $params[] = $date_to;
}

$where_clause = implode(' AND ', $where_conditions);

try {
    // عد إجمالي المعاملات
    $total_transactions = $database->count('transactions t', $where_clause, $params);
    
    // جلب المعاملات مع الترقيم
    $sql = "SELECT t.*, 
                   p.title as property_title, p.address as property_address,
                   c.name as client_name, c.phone as client_phone,
                   u.full_name as agent_name
            FROM transactions t 
            LEFT JOIN properties p ON t.property_id = p.id 
            LEFT JOIN clients c ON t.client_id = c.id 
            LEFT JOIN users u ON t.user_id = u.id 
            WHERE {$where_clause} 
            ORDER BY t.created_at DESC 
            LIMIT {$per_page} OFFSET {$offset}";
    
    $transactions = $database->fetchAll($sql, $params);
    
    // حساب عدد الصفحات
    $total_pages = ceil($total_transactions / $per_page);
    
    // إحصائيات المعاملات
    $stats = [
        'total' => $database->count('transactions', 'company_id = ?', [$company_id]),
        'pending' => $database->count('transactions', 'company_id = ? AND status = ?', [$company_id, 'pending']),
        'completed' => $database->count('transactions', 'company_id = ? AND status = ?', [$company_id, 'completed']),
        'cancelled' => $database->count('transactions', 'company_id = ? AND status = ?', [$company_id, 'cancelled'])
    ];
    
    // إجمالي العمولات
    $commission_result = $database->fetch(
        "SELECT SUM(commission) as total_commission FROM transactions WHERE company_id = ? AND status = 'completed'", 
        [$company_id]
    );
    $total_commission = $commission_result['total_commission'] ?? 0;
    
    // إجمالي المبيعات
    $sales_result = $database->fetch(
        "SELECT SUM(amount) as total_sales FROM transactions WHERE company_id = ? AND status = 'completed'", 
        [$company_id]
    );
    $total_sales = $sales_result['total_sales'] ?? 0;
    
} catch (Exception $e) {
    $error_message = "حدث خطأ في جلب البيانات";
    $transactions = [];
    $total_transactions = 0;
    $total_pages = 0;
    $stats = [];
}

// معالجة تحديث حالة المعاملة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $transaction_id = intval($_POST['transaction_id']);
    $new_status = sanitize($_POST['status']);
    
    $valid_statuses = ['pending', 'completed', 'cancelled'];
    
    if (in_array($new_status, $valid_statuses)) {
        try {
            // التحقق من ملكية المعاملة
            $transaction = $database->fetch(
                "SELECT * FROM transactions WHERE id = ? AND company_id = ?", 
                [$transaction_id, $company_id]
            );
            
            if ($transaction) {
                $database->update('transactions', ['status' => $new_status], 'id = ?', [$transaction_id]);
                
                // تحديث حالة العقار إذا تم إكمال المعاملة
                if ($new_status === 'completed') {
                    $property_status = $transaction['transaction_type'] === 'sale' ? 'sold' : 'rented';
                    $database->update('properties', ['status' => $property_status], 'id = ?', [$transaction['property_id']]);
                } elseif ($transaction['status'] === 'completed' && $new_status !== 'completed') {
                    // إذا تم إلغاء معاملة مكتملة، أعد العقار لحالة متاح
                    $database->update('properties', ['status' => 'available'], 'id = ?', [$transaction['property_id']]);
                }
                
                setAlert('تم تحديث حالة المعاملة بنجاح', 'success');
            } else {
                setAlert('المعاملة غير موجودة', 'error');
            }
        } catch (Exception $e) {
            setAlert('حدث خطأ أثناء تحديث الحالة', 'error');
        }
    }
    
    redirect('index.php');
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المعاملات - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <style>
        .transaction-card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            border-left: 4px solid transparent;
        }
        
        .transaction-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        
        .transaction-card.pending {
            border-left-color: #ffc107;
        }
        
        .transaction-card.completed {
            border-left-color: #28a745;
        }
        
        .transaction-card.cancelled {
            border-left-color: #dc3545;
        }
        
        .stats-summary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .transaction-amount {
            font-size: 1.2rem;
            font-weight: 700;
        }
        
        .transaction-amount.sale {
            color: #28a745;
        }
        
        .transaction-amount.rent {
            color: #17a2b8;
        }
        
        .commission-badge {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
        }
        
        .status-dropdown {
            min-width: 120px;
        }
        
        .property-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
        }
        
        .client-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .client-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2>إدارة المعاملات</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="../dashboard.php">لوحة التحكم</a></li>
                                <li class="breadcrumb-item active">المعاملات</li>
                            </ol>
                        </nav>
                    </div>
                    <a href="add.php" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>
                        إضافة معاملة جديدة
                    </a>
                </div>
                
                <!-- Alerts -->
                <?php showAlert(); ?>
                
                <!-- Stats Summary -->
                <div class="stats-summary">
                    <div class="row text-center">
                        <div class="col-md-2">
                            <h3><?php echo formatNumber($stats['total'] ?? 0); ?></h3>
                            <small>إجمالي المعاملات</small>
                        </div>
                        <div class="col-md-2">
                            <h3><?php echo formatNumber($stats['pending'] ?? 0); ?></h3>
                            <small>معلقة</small>
                        </div>
                        <div class="col-md-2">
                            <h3><?php echo formatNumber($stats['completed'] ?? 0); ?></h3>
                            <small>مكتملة</small>
                        </div>
                        <div class="col-md-2">
                            <h3><?php echo formatNumber($stats['cancelled'] ?? 0); ?></h3>
                            <small>ملغية</small>
                        </div>
                        <div class="col-md-2">
                            <h3><?php echo formatCurrency($total_sales); ?></h3>
                            <small>إجمالي المبيعات</small>
                        </div>
                        <div class="col-md-2">
                            <h3><?php echo formatCurrency($total_commission); ?></h3>
                            <small>إجمالي العمولات</small>
                        </div>
                    </div>
                </div>
                
                <!-- Filters -->
                <div class="filter-section">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <input type="text" class="form-control" name="search" 
                                   placeholder="البحث في المعاملات..." 
                                   value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        
                        <div class="col-md-2">
                            <select name="status" class="form-select">
                                <option value="">جميع الحالات</option>
                                <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>معلقة</option>
                                <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>مكتملة</option>
                                <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>ملغية</option>
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <select name="type" class="form-select">
                                <option value="">جميع الأنواع</option>
                                <option value="sale" <?php echo $type_filter === 'sale' ? 'selected' : ''; ?>>بيع</option>
                                <option value="rent" <?php echo $type_filter === 'rent' ? 'selected' : ''; ?>>إيجار</option>
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <input type="date" class="form-control" name="date_from" 
                                   placeholder="من تاريخ" value="<?php echo $date_from; ?>">
                        </div>
                        
                        <div class="col-md-2">
                            <input type="date" class="form-control" name="date_to" 
                                   placeholder="إلى تاريخ" value="<?php echo $date_to; ?>">
                        </div>
                        
                        <div class="col-md-1">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="bi bi-funnel"></i>
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- Transactions List -->
                <?php if (empty($transactions)): ?>
                    <div class="text-center py-5">
                        <i class="bi bi-receipt display-1 text-muted"></i>
                        <h4 class="mt-3">لا توجد معاملات</h4>
                        <p class="text-muted">لم يتم العثور على معاملات تطابق معايير البحث</p>
                        <a href="add.php" class="btn btn-primary">إضافة معاملة جديدة</a>
                    </div>
                <?php else: ?>
                    <?php foreach ($transactions as $transaction): ?>
                        <div class="card transaction-card <?php echo $transaction['status']; ?>">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <!-- Property Info -->
                                        <div class="property-info">
                                            <h6 class="mb-1">
                                                <i class="bi bi-building me-2"></i>
                                                <?php echo htmlspecialchars($transaction['property_title']); ?>
                                            </h6>
                                            <small class="text-muted">
                                                <i class="bi bi-geo-alt me-1"></i>
                                                <?php echo htmlspecialchars($transaction['property_address']); ?>
                                            </small>
                                        </div>
                                        
                                        <!-- Client Info -->
                                        <div class="client-info">
                                            <div class="client-avatar">
                                                <?php echo strtoupper(substr($transaction['client_name'], 0, 2)); ?>
                                            </div>
                                            <div>
                                                <strong><?php echo htmlspecialchars($transaction['client_name']); ?></strong>
                                                <br>
                                                <small class="text-muted">
                                                    <i class="bi bi-telephone me-1"></i>
                                                    <?php echo htmlspecialchars($transaction['client_phone']); ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <!-- Transaction Details -->
                                        <div class="text-center">
                                            <div class="transaction-amount <?php echo $transaction['transaction_type']; ?>">
                                                <?php echo formatCurrency($transaction['amount']); ?>
                                            </div>
                                            <small class="text-muted">
                                                <?php echo TRANSACTION_TYPES[$transaction['transaction_type']]; ?>
                                            </small>
                                            
                                            <?php if ($transaction['commission']): ?>
                                                <div class="mt-2">
                                                    <span class="commission-badge">
                                                        عمولة: <?php echo formatCurrency($transaction['commission']); ?>
                                                    </span>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <!-- Status and Actions -->
                                        <div class="text-center">
                                            <form method="POST" class="d-inline">
                                                <input type="hidden" name="transaction_id" value="<?php echo $transaction['id']; ?>">
                                                <select name="status" class="form-select status-dropdown mb-2" 
                                                        onchange="this.form.submit()">
                                                    <option value="pending" <?php echo $transaction['status'] === 'pending' ? 'selected' : ''; ?>>
                                                        معلقة
                                                    </option>
                                                    <option value="completed" <?php echo $transaction['status'] === 'completed' ? 'selected' : ''; ?>>
                                                        مكتملة
                                                    </option>
                                                    <option value="cancelled" <?php echo $transaction['status'] === 'cancelled' ? 'selected' : ''; ?>>
                                                        ملغية
                                                    </option>
                                                </select>
                                                <input type="hidden" name="update_status" value="1">
                                            </form>
                                            
                                            <div class="btn-group w-100" role="group">
                                                <a href="view.php?id=<?php echo $transaction['id']; ?>" 
                                                   class="btn btn-outline-primary btn-sm">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="edit.php?id=<?php echo $transaction['id']; ?>" 
                                                   class="btn btn-outline-secondary btn-sm">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <a href="print.php?id=<?php echo $transaction['id']; ?>" 
                                                   class="btn btn-outline-info btn-sm" target="_blank">
                                                    <i class="bi bi-printer"></i>
                                                </a>
                                            </div>
                                            
                                            <div class="mt-2">
                                                <small class="text-muted">
                                                    <i class="bi bi-calendar me-1"></i>
                                                    <?php echo formatDate($transaction['created_at'], 'arabic_date'); ?>
                                                </small>
                                                <?php if ($transaction['agent_name']): ?>
                                                    <br>
                                                    <small class="text-muted">
                                                        <i class="bi bi-person-badge me-1"></i>
                                                        <?php echo htmlspecialchars($transaction['agent_name']); ?>
                                                    </small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <?php if ($transaction['notes']): ?>
                                    <div class="row mt-3">
                                        <div class="col-12">
                                            <div class="alert alert-light mb-0">
                                                <small>
                                                    <i class="bi bi-chat-text me-2"></i>
                                                    <strong>ملاحظات:</strong> 
                                                    <?php echo nl2br(htmlspecialchars($transaction['notes'])); ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    
                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                        <nav aria-label="تنقل الصفحات" class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page - 1; ?>&<?php echo http_build_query(array_filter($_GET, fn($key) => $key !== 'page', ARRAY_FILTER_USE_KEY)); ?>">
                                            السابق
                                        </a>
                                    </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>&<?php echo http_build_query(array_filter($_GET, fn($key) => $key !== 'page', ARRAY_FILTER_USE_KEY)); ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>
                                
                                <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page + 1; ?>&<?php echo http_build_query(array_filter($_GET, fn($key) => $key !== 'page', ARRAY_FILTER_USE_KEY)); ?>">
                                            التالي
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تحسين تجربة البحث
        let searchTimeout;
        document.querySelector('input[name="search"]').addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.form.submit();
            }, 500);
        });
        
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.transaction-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 50);
            });
        });
        
        // تأكيد تغيير الحالة
        document.querySelectorAll('.status-dropdown').forEach(select => {
            select.addEventListener('change', function(e) {
                const currentStatus = this.dataset.currentStatus || this.querySelector('option[selected]')?.value;
                const newStatus = this.value;
                
                if (currentStatus === 'completed' && newStatus !== 'completed') {
                    if (!confirm('تغيير حالة المعاملة من "مكتملة" سيؤثر على حالة العقار. هل أنت متأكد؟')) {
                        this.value = currentStatus;
                        e.preventDefault();
                        return false;
                    }
                }
                
                if (newStatus === 'completed') {
                    if (!confirm('تأكيد إكمال المعاملة؟ سيتم تحديث حالة العقار تلقائياً.')) {
                        this.value = currentStatus;
                        e.preventDefault();
                        return false;
                    }
                }
            });
        });
    </script>
</body>
</html>
