<?php
/**
 * صفحة عرض تفاصيل العقار
 * Property Details View Page
 */

require_once '../../config/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../../login.php');
}

$user = getCurrentUser();
$company_id = $user['company_id'];

// الحصول على معرف العقار
$property_id = intval($_GET['id'] ?? 0);

if (!$property_id) {
    setAlert('معرف العقار غير صحيح', 'error');
    redirect('index.php');
}

try {
    // جلب تفاصيل العقار
    $sql = "SELECT p.*, u.full_name as agent_name, u.phone as agent_phone, u.email as agent_email
            FROM properties p 
            LEFT JOIN users u ON p.user_id = u.id 
            WHERE p.id = ? AND p.company_id = ?";
    
    $property = $database->fetch($sql, [$property_id, $company_id]);
    
    if (!$property) {
        setAlert('العقار غير موجود', 'error');
        redirect('index.php');
    }
    
    // جلب صور العقار
    $images = $database->fetchAll(
        "SELECT * FROM property_images WHERE property_id = ? ORDER BY is_primary DESC, sort_order ASC", 
        [$property_id]
    );
    
    // تحديث عدد المشاهدات
    $database->query("UPDATE properties SET views_count = views_count + 1 WHERE id = ?", [$property_id]);
    
    // جلب المعاملات المرتبطة بالعقار
    $transactions = $database->fetchAll(
        "SELECT t.*, c.name as client_name, c.phone as client_phone 
         FROM transactions t 
         LEFT JOIN clients c ON t.client_id = c.id 
         WHERE t.property_id = ? 
         ORDER BY t.created_at DESC", 
        [$property_id]
    );
    
} catch (Exception $e) {
    setAlert('حدث خطأ في جلب البيانات', 'error');
    redirect('index.php');
}

// معالجة تحديث حالة العقار
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $new_status = sanitize($_POST['status']);
    
    if (in_array($new_status, array_keys(PROPERTY_STATUS))) {
        try {
            $database->update('properties', ['status' => $new_status], 'id = ?', [$property_id]);
            $property['status'] = $new_status;
            setAlert('تم تحديث حالة العقار بنجاح', 'success');
        } catch (Exception $e) {
            setAlert('حدث خطأ أثناء تحديث الحالة', 'error');
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($property['title']); ?> - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <style>
        .property-gallery {
            position: relative;
            margin-bottom: 2rem;
        }
        
        .main-image {
            width: 100%;
            height: 400px;
            object-fit: cover;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .thumbnail-gallery {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
            overflow-x: auto;
            padding: 0.5rem 0;
        }
        
        .thumbnail {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 8px;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            flex-shrink: 0;
        }
        
        .thumbnail:hover,
        .thumbnail.active {
            border-color: #667eea;
            transform: scale(1.05);
        }
        
        .property-status-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            z-index: 2;
        }
        
        .property-details-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .detail-item {
            display: flex;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .detail-item:last-child {
            border-bottom: none;
        }
        
        .detail-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 1rem;
            flex-shrink: 0;
        }
        
        .detail-content {
            flex-grow: 1;
        }
        
        .detail-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 0.25rem;
        }
        
        .detail-value {
            color: #666;
            font-size: 0.9rem;
        }
        
        .price-display {
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
            text-align: center;
            padding: 1rem;
            background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        
        .action-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-bottom: 2rem;
        }
        
        .transaction-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 4px solid #667eea;
        }
        
        .no-image-placeholder {
            width: 100%;
            height: 400px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 15px;
            color: #6c757d;
        }
        
        .map-container {
            height: 300px;
            border-radius: 10px;
            overflow: hidden;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2><?php echo htmlspecialchars($property['title']); ?></h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="../dashboard.php">لوحة التحكم</a></li>
                                <li class="breadcrumb-item"><a href="index.php">العقارات</a></li>
                                <li class="breadcrumb-item active">تفاصيل العقار</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="action-buttons">
                        <a href="edit.php?id=<?php echo $property['id']; ?>" class="btn btn-primary">
                            <i class="bi bi-pencil me-2"></i>
                            تعديل
                        </a>
                        <a href="index.php" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-right me-2"></i>
                            العودة
                        </a>
                    </div>
                </div>
                
                <!-- Alerts -->
                <?php showAlert(); ?>
                
                <div class="row">
                    <!-- Left Column - Images and Details -->
                    <div class="col-lg-8">
                        <!-- Property Gallery -->
                        <div class="property-gallery">
                            <?php if (!empty($images)): ?>
                                <div class="position-relative">
                                    <img src="../../uploads/properties/<?php echo $images[0]['image_path']; ?>" 
                                         alt="<?php echo htmlspecialchars($property['title']); ?>" 
                                         class="main-image" id="mainImage">
                                    
                                    <div class="property-status-badge">
                                        <?php
                                        $status_class = '';
                                        switch ($property['status']) {
                                            case 'available': $status_class = 'bg-success'; break;
                                            case 'sold': $status_class = 'bg-danger'; break;
                                            case 'rented': $status_class = 'bg-warning'; break;
                                            case 'pending': $status_class = 'bg-info'; break;
                                        }
                                        ?>
                                        <span class="badge <?php echo $status_class; ?> fs-6">
                                            <?php echo PROPERTY_STATUS[$property['status']]; ?>
                                        </span>
                                    </div>
                                </div>
                                
                                <?php if (count($images) > 1): ?>
                                    <div class="thumbnail-gallery">
                                        <?php foreach ($images as $index => $image): ?>
                                            <img src="../../uploads/properties/<?php echo $image['image_path']; ?>" 
                                                 alt="صورة <?php echo $index + 1; ?>" 
                                                 class="thumbnail <?php echo $index === 0 ? 'active' : ''; ?>"
                                                 onclick="changeMainImage('../../uploads/properties/<?php echo $image['image_path']; ?>', this)">
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            <?php else: ?>
                                <div class="no-image-placeholder">
                                    <div class="text-center">
                                        <i class="bi bi-image display-1"></i>
                                        <p class="mt-2">لا توجد صور للعقار</p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Property Description -->
                        <?php if ($property['description']): ?>
                            <div class="property-details-card">
                                <h5 class="mb-3">
                                    <i class="bi bi-file-text me-2"></i>
                                    وصف العقار
                                </h5>
                                <p class="text-muted"><?php echo nl2br(htmlspecialchars($property['description'])); ?></p>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Location Map -->
                        <?php if ($property['latitude'] && $property['longitude']): ?>
                            <div class="property-details-card">
                                <h5 class="mb-3">
                                    <i class="bi bi-geo-alt me-2"></i>
                                    الموقع على الخريطة
                                </h5>
                                <div class="map-container">
                                    <iframe 
                                        src="https://www.google.com/maps/embed?pb=!1m14!1m12!1m3!1d3624.0!2d<?php echo $property['longitude']; ?>!3d<?php echo $property['latitude']; ?>!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!5e0!3m2!1sar!2ssa!4v1234567890"
                                        width="100%" 
                                        height="300" 
                                        style="border:0;" 
                                        allowfullscreen="" 
                                        loading="lazy">
                                    </iframe>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Transactions History -->
                        <?php if (!empty($transactions)): ?>
                            <div class="property-details-card">
                                <h5 class="mb-3">
                                    <i class="bi bi-clock-history me-2"></i>
                                    تاريخ المعاملات
                                </h5>
                                <?php foreach ($transactions as $transaction): ?>
                                    <div class="transaction-item">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="mb-1">
                                                    <?php echo TRANSACTION_TYPES[$transaction['transaction_type']]; ?>
                                                    - <?php echo htmlspecialchars($transaction['client_name']); ?>
                                                </h6>
                                                <small class="text-muted">
                                                    <i class="bi bi-calendar me-1"></i>
                                                    <?php echo formatDate($transaction['created_at'], 'arabic_datetime'); ?>
                                                </small>
                                            </div>
                                            <div class="text-end">
                                                <div class="fw-bold text-success">
                                                    <?php echo formatCurrency($transaction['amount']); ?>
                                                </div>
                                                <span class="badge bg-<?php echo $transaction['status'] === 'completed' ? 'success' : ($transaction['status'] === 'pending' ? 'warning' : 'danger'); ?>">
                                                    <?php 
                                                    $status_labels = [
                                                        'pending' => 'معلقة',
                                                        'completed' => 'مكتملة',
                                                        'cancelled' => 'ملغية'
                                                    ];
                                                    echo $status_labels[$transaction['status']];
                                                    ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Right Column - Property Info -->
                    <div class="col-lg-4">
                        <!-- Price -->
                        <div class="price-display">
                            <?php echo formatCurrency($property['price']); ?>
                        </div>
                        
                        <!-- Quick Actions -->
                        <div class="property-details-card">
                            <h5 class="mb-3">إجراءات سريعة</h5>
                            
                            <!-- Status Update -->
                            <form method="POST" class="mb-3">
                                <div class="input-group">
                                    <select name="status" class="form-select">
                                        <?php foreach (PROPERTY_STATUS as $key => $value): ?>
                                            <option value="<?php echo $key; ?>" <?php echo $property['status'] === $key ? 'selected' : ''; ?>>
                                                <?php echo $value; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <button type="submit" name="update_status" class="btn btn-outline-primary">
                                        تحديث
                                    </button>
                                </div>
                            </form>
                            
                            <div class="d-grid gap-2">
                                <a href="../transactions/add.php?property_id=<?php echo $property['id']; ?>" class="btn btn-success">
                                    <i class="bi bi-plus-circle me-2"></i>
                                    إضافة معاملة
                                </a>
                                <a href="edit.php?id=<?php echo $property['id']; ?>" class="btn btn-primary">
                                    <i class="bi bi-pencil me-2"></i>
                                    تعديل العقار
                                </a>
                                <button type="button" class="btn btn-outline-info" onclick="shareProperty()">
                                    <i class="bi bi-share me-2"></i>
                                    مشاركة
                                </button>
                            </div>
                        </div>
                        
                        <!-- Property Details -->
                        <div class="property-details-card">
                            <h5 class="mb-3">تفاصيل العقار</h5>
                            
                            <div class="detail-item">
                                <div class="detail-icon">
                                    <i class="bi bi-building"></i>
                                </div>
                                <div class="detail-content">
                                    <div class="detail-label">نوع العقار</div>
                                    <div class="detail-value"><?php echo PROPERTY_TYPES[$property['property_type']]; ?></div>
                                </div>
                            </div>
                            
                            <div class="detail-item">
                                <div class="detail-icon">
                                    <i class="bi bi-arrow-left-right"></i>
                                </div>
                                <div class="detail-content">
                                    <div class="detail-label">نوع المعاملة</div>
                                    <div class="detail-value"><?php echo TRANSACTION_TYPES[$property['transaction_type']]; ?></div>
                                </div>
                            </div>
                            
                            <?php if ($property['area']): ?>
                                <div class="detail-item">
                                    <div class="detail-icon">
                                        <i class="bi bi-rulers"></i>
                                    </div>
                                    <div class="detail-content">
                                        <div class="detail-label">المساحة</div>
                                        <div class="detail-value"><?php echo formatArea($property['area']); ?></div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($property['bedrooms']): ?>
                                <div class="detail-item">
                                    <div class="detail-icon">
                                        <i class="bi bi-door-closed"></i>
                                    </div>
                                    <div class="detail-content">
                                        <div class="detail-label">غرف النوم</div>
                                        <div class="detail-value"><?php echo $property['bedrooms']; ?> غرف</div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($property['bathrooms']): ?>
                                <div class="detail-item">
                                    <div class="detail-icon">
                                        <i class="bi bi-droplet"></i>
                                    </div>
                                    <div class="detail-content">
                                        <div class="detail-label">دورات المياه</div>
                                        <div class="detail-value"><?php echo $property['bathrooms']; ?> حمام</div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <div class="detail-item">
                                <div class="detail-icon">
                                    <i class="bi bi-sofa"></i>
                                </div>
                                <div class="detail-content">
                                    <div class="detail-label">حالة الأثاث</div>
                                    <div class="detail-value">
                                        <?php 
                                        $furnished_labels = [
                                            'furnished' => 'مفروش بالكامل',
                                            'semi_furnished' => 'نصف مفروش',
                                            'unfurnished' => 'غير مفروش'
                                        ];
                                        echo $furnished_labels[$property['furnished']];
                                        ?>
                                    </div>
                                </div>
                            </div>
                            
                            <?php if ($property['parking']): ?>
                                <div class="detail-item">
                                    <div class="detail-icon">
                                        <i class="bi bi-car-front"></i>
                                    </div>
                                    <div class="detail-content">
                                        <div class="detail-label">موقف سيارة</div>
                                        <div class="detail-value">متوفر</div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <div class="detail-item">
                                <div class="detail-icon">
                                    <i class="bi bi-geo-alt"></i>
                                </div>
                                <div class="detail-content">
                                    <div class="detail-label">الموقع</div>
                                    <div class="detail-value">
                                        <?php echo htmlspecialchars($property['city']); ?>
                                        <?php if ($property['district']): ?>
                                            - <?php echo htmlspecialchars($property['district']); ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="detail-item">
                                <div class="detail-icon">
                                    <i class="bi bi-eye"></i>
                                </div>
                                <div class="detail-content">
                                    <div class="detail-label">المشاهدات</div>
                                    <div class="detail-value"><?php echo formatNumber($property['views_count']); ?> مشاهدة</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Agent Info -->
                        <?php if ($property['agent_name']): ?>
                            <div class="property-details-card">
                                <h5 class="mb-3">معلومات الوسيط</h5>
                                
                                <div class="detail-item">
                                    <div class="detail-icon">
                                        <i class="bi bi-person"></i>
                                    </div>
                                    <div class="detail-content">
                                        <div class="detail-label">الاسم</div>
                                        <div class="detail-value"><?php echo htmlspecialchars($property['agent_name']); ?></div>
                                    </div>
                                </div>
                                
                                <?php if ($property['agent_phone']): ?>
                                    <div class="detail-item">
                                        <div class="detail-icon">
                                            <i class="bi bi-phone"></i>
                                        </div>
                                        <div class="detail-content">
                                            <div class="detail-label">الهاتف</div>
                                            <div class="detail-value">
                                                <a href="tel:<?php echo $property['agent_phone']; ?>">
                                                    <?php echo htmlspecialchars($property['agent_phone']); ?>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($property['agent_email']): ?>
                                    <div class="detail-item">
                                        <div class="detail-icon">
                                            <i class="bi bi-envelope"></i>
                                        </div>
                                        <div class="detail-content">
                                            <div class="detail-label">البريد الإلكتروني</div>
                                            <div class="detail-value">
                                                <a href="mailto:<?php echo $property['agent_email']; ?>">
                                                    <?php echo htmlspecialchars($property['agent_email']); ?>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Property Meta -->
                        <div class="property-details-card">
                            <h5 class="mb-3">معلومات إضافية</h5>
                            
                            <div class="detail-item">
                                <div class="detail-icon">
                                    <i class="bi bi-calendar-plus"></i>
                                </div>
                                <div class="detail-content">
                                    <div class="detail-label">تاريخ الإضافة</div>
                                    <div class="detail-value"><?php echo formatDate($property['created_at'], 'arabic_datetime'); ?></div>
                                </div>
                            </div>
                            
                            <div class="detail-item">
                                <div class="detail-icon">
                                    <i class="bi bi-calendar-check"></i>
                                </div>
                                <div class="detail-content">
                                    <div class="detail-label">آخر تحديث</div>
                                    <div class="detail-value"><?php echo formatDate($property['updated_at'], 'arabic_datetime'); ?></div>
                                </div>
                            </div>
                            
                            <div class="detail-item">
                                <div class="detail-icon">
                                    <i class="bi bi-hash"></i>
                                </div>
                                <div class="detail-content">
                                    <div class="detail-label">رقم العقار</div>
                                    <div class="detail-value">#<?php echo $property['id']; ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تغيير الصورة الرئيسية
        function changeMainImage(src, thumbnail) {
            document.getElementById('mainImage').src = src;
            
            // إزالة الفئة النشطة من جميع المصغرات
            document.querySelectorAll('.thumbnail').forEach(thumb => {
                thumb.classList.remove('active');
            });
            
            // إضافة الفئة النشطة للمصغرة المحددة
            thumbnail.classList.add('active');
        }
        
        // مشاركة العقار
        function shareProperty() {
            if (navigator.share) {
                navigator.share({
                    title: '<?php echo htmlspecialchars($property['title']); ?>',
                    text: 'شاهد هذا العقار المميز',
                    url: window.location.href
                });
            } else {
                // نسخ الرابط إلى الحافظة
                navigator.clipboard.writeText(window.location.href).then(() => {
                    alert('تم نسخ رابط العقار إلى الحافظة');
                });
            }
        }
        
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير ظهور البطاقات
            const cards = document.querySelectorAll('.property-details-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
