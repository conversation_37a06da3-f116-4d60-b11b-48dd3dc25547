<?php
/**
 * صفحة إضافة عقار جديد
 * Add New Property Page
 */

require_once '../../config/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../../login.php');
}

$user = getCurrentUser();
$company_id = $user['company_id'];

$error_message = '';
$success_message = '';

// معالجة إضافة العقار
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = [
        'title' => sanitize($_POST['title'] ?? ''),
        'description' => sanitize($_POST['description'] ?? ''),
        'property_type' => sanitize($_POST['property_type'] ?? ''),
        'transaction_type' => sanitize($_POST['transaction_type'] ?? ''),
        'price' => floatval($_POST['price'] ?? 0),
        'area' => floatval($_POST['area'] ?? 0),
        'bedrooms' => intval($_POST['bedrooms'] ?? 0),
        'bathrooms' => intval($_POST['bathrooms'] ?? 0),
        'floor_number' => intval($_POST['floor_number'] ?? 0),
        'total_floors' => intval($_POST['total_floors'] ?? 0),
        'parking' => isset($_POST['parking']) ? 1 : 0,
        'furnished' => sanitize($_POST['furnished'] ?? 'unfurnished'),
        'address' => sanitize($_POST['address'] ?? ''),
        'city' => sanitize($_POST['city'] ?? ''),
        'district' => sanitize($_POST['district'] ?? ''),
        'latitude' => floatval($_POST['latitude'] ?? 0),
        'longitude' => floatval($_POST['longitude'] ?? 0),
        'featured' => isset($_POST['featured']) ? 1 : 0
    ];
    
    // التحقق من البيانات المطلوبة
    if (empty($data['title']) || empty($data['property_type']) || empty($data['transaction_type']) || 
        empty($data['price']) || empty($data['address']) || empty($data['city'])) {
        $error_message = 'جميع الحقول المطلوبة يجب ملؤها';
    } elseif ($data['price'] <= 0) {
        $error_message = 'السعر يجب أن يكون أكبر من صفر';
    } else {
        try {
            // بدء المعاملة
            $database->conn->beginTransaction();
            
            // إضافة بيانات الشركة والمستخدم
            $data['company_id'] = $company_id;
            $data['user_id'] = $user['id'];
            $data['status'] = 'available';
            
            // إدراج العقار
            $property_id = $database->insert('properties', $data);
            
            // معالجة رفع الصور
            if (!empty($_FILES['images']['name'][0])) {
                $upload_dir = '../../uploads/properties/';
                
                // إنشاء المجلد إذا لم يكن موجوداً
                if (!file_exists($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }
                
                $is_primary = true;
                $sort_order = 0;
                
                foreach ($_FILES['images']['tmp_name'] as $key => $tmp_name) {
                    if (!empty($tmp_name)) {
                        $file_info = [
                            'name' => $_FILES['images']['name'][$key],
                            'tmp_name' => $tmp_name,
                            'size' => $_FILES['images']['size'][$key],
                            'error' => $_FILES['images']['error'][$key]
                        ];
                        
                        $upload_result = uploadFile($file_info, $upload_dir, ALLOWED_IMAGE_TYPES);
                        
                        if ($upload_result['success']) {
                            // إدراج معلومات الصورة
                            $image_data = [
                                'property_id' => $property_id,
                                'image_path' => $upload_result['filename'],
                                'is_primary' => $is_primary ? 1 : 0,
                                'sort_order' => $sort_order++
                            ];
                            
                            $database->insert('property_images', $image_data);
                            
                            // تغيير حجم الصورة
                            $source_path = $upload_result['path'];
                            $thumbnail_path = $upload_dir . 'thumb_' . $upload_result['filename'];
                            resizeImage($source_path, $thumbnail_path, THUMBNAIL_WIDTH, THUMBNAIL_HEIGHT);
                            
                            $is_primary = false; // الصورة الأولى فقط تكون رئيسية
                        }
                    }
                }
            }
            
            // تأكيد المعاملة
            $database->conn->commit();
            
            setAlert('تم إضافة العقار بنجاح', 'success');
            redirect('view.php?id=' . $property_id);
            
        } catch (Exception $e) {
            // إلغاء المعاملة
            $database->conn->rollback();
            $error_message = 'حدث خطأ أثناء إضافة العقار: ' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة عقار جديد - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <style>
        .form-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .section-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
        }
        
        .image-upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .image-upload-area:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }
        
        .image-upload-area.dragover {
            border-color: #667eea;
            background-color: #f0f4ff;
        }
        
        .image-preview {
            display: none;
            margin-top: 1rem;
        }
        
        .preview-item {
            position: relative;
            display: inline-block;
            margin: 0.5rem;
        }
        
        .preview-image {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 8px;
            border: 2px solid #ddd;
        }
        
        .remove-image {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            font-size: 12px;
            cursor: pointer;
        }
        
        .map-container {
            height: 300px;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .required {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2>إضافة عقار جديد</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="../dashboard.php">لوحة التحكم</a></li>
                                <li class="breadcrumb-item"><a href="index.php">العقارات</a></li>
                                <li class="breadcrumb-item active">إضافة عقار</li>
                            </ol>
                        </nav>
                    </div>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-right me-2"></i>
                        العودة للقائمة
                    </a>
                </div>
                
                <!-- Alerts -->
                <?php if ($error_message): ?>
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>
                
                <?php showAlert(); ?>
                
                <!-- Form -->
                <form method="POST" enctype="multipart/form-data" id="propertyForm">
                    <!-- Basic Information -->
                    <div class="form-section">
                        <h5 class="section-title">
                            <i class="bi bi-info-circle me-2"></i>
                            المعلومات الأساسية
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="title" class="form-label">
                                        عنوان العقار <span class="required">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="title" name="title" 
                                           placeholder="مثال: شقة فاخرة في حي الملز"
                                           value="<?php echo htmlspecialchars($data['title'] ?? ''); ?>" required>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="price" class="form-label">
                                        السعر (ريال سعودي) <span class="required">*</span>
                                    </label>
                                    <input type="number" class="form-control" id="price" name="price" 
                                           placeholder="0" min="0" step="0.01"
                                           value="<?php echo $data['price'] ?? ''; ?>" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">وصف العقار</label>
                            <textarea class="form-control" id="description" name="description" rows="4"
                                      placeholder="اكتب وصفاً مفصلاً للعقار..."><?php echo htmlspecialchars($data['description'] ?? ''); ?></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="property_type" class="form-label">
                                        نوع العقار <span class="required">*</span>
                                    </label>
                                    <select class="form-select" id="property_type" name="property_type" required>
                                        <option value="">اختر نوع العقار</option>
                                        <?php foreach (PROPERTY_TYPES as $key => $value): ?>
                                            <option value="<?php echo $key; ?>" 
                                                    <?php echo ($data['property_type'] ?? '') === $key ? 'selected' : ''; ?>>
                                                <?php echo $value; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="transaction_type" class="form-label">
                                        نوع المعاملة <span class="required">*</span>
                                    </label>
                                    <select class="form-select" id="transaction_type" name="transaction_type" required>
                                        <option value="">اختر نوع المعاملة</option>
                                        <?php foreach (TRANSACTION_TYPES as $key => $value): ?>
                                            <option value="<?php echo $key; ?>" 
                                                    <?php echo ($data['transaction_type'] ?? '') === $key ? 'selected' : ''; ?>>
                                                <?php echo $value; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="furnished" class="form-label">حالة الأثاث</label>
                                    <select class="form-select" id="furnished" name="furnished">
                                        <option value="unfurnished" <?php echo ($data['furnished'] ?? 'unfurnished') === 'unfurnished' ? 'selected' : ''; ?>>
                                            غير مفروش
                                        </option>
                                        <option value="semi_furnished" <?php echo ($data['furnished'] ?? '') === 'semi_furnished' ? 'selected' : ''; ?>>
                                            نصف مفروش
                                        </option>
                                        <option value="furnished" <?php echo ($data['furnished'] ?? '') === 'furnished' ? 'selected' : ''; ?>>
                                            مفروش بالكامل
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Property Details -->
                    <div class="form-section">
                        <h5 class="section-title">
                            <i class="bi bi-rulers me-2"></i>
                            تفاصيل العقار
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="area" class="form-label">المساحة (متر مربع)</label>
                                    <input type="number" class="form-control" id="area" name="area" 
                                           placeholder="0" min="0" step="0.1"
                                           value="<?php echo $data['area'] ?? ''; ?>">
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="bedrooms" class="form-label">عدد غرف النوم</label>
                                    <input type="number" class="form-control" id="bedrooms" name="bedrooms" 
                                           placeholder="0" min="0" max="20"
                                           value="<?php echo $data['bedrooms'] ?? ''; ?>">
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="bathrooms" class="form-label">عدد دورات المياه</label>
                                    <input type="number" class="form-control" id="bathrooms" name="bathrooms" 
                                           placeholder="0" min="0" max="10"
                                           value="<?php echo $data['bathrooms'] ?? ''; ?>">
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="parking" name="parking"
                                           <?php echo isset($data['parking']) && $data['parking'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="parking">
                                        موقف سيارة
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="floor_number" class="form-label">رقم الطابق</label>
                                    <input type="number" class="form-control" id="floor_number" name="floor_number" 
                                           placeholder="0" min="0" max="100"
                                           value="<?php echo $data['floor_number'] ?? ''; ?>">
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="total_floors" class="form-label">إجمالي الطوابق</label>
                                    <input type="number" class="form-control" id="total_floors" name="total_floors" 
                                           placeholder="0" min="0" max="100"
                                           value="<?php echo $data['total_floors'] ?? ''; ?>">
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="featured" name="featured"
                                   <?php echo isset($data['featured']) && $data['featured'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="featured">
                                عقار مميز (سيظهر في المقدمة)
                            </label>
                        </div>
                    </div>
                    
                    <!-- Location -->
                    <div class="form-section">
                        <h5 class="section-title">
                            <i class="bi bi-geo-alt me-2"></i>
                            الموقع
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="city" class="form-label">
                                        المدينة <span class="required">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="city" name="city" 
                                           placeholder="مثال: الرياض"
                                           value="<?php echo htmlspecialchars($data['city'] ?? ''); ?>" required>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="district" class="form-label">الحي</label>
                                    <input type="text" class="form-control" id="district" name="district" 
                                           placeholder="مثال: الملز"
                                           value="<?php echo htmlspecialchars($data['district'] ?? ''); ?>">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="address" class="form-label">
                                العنوان التفصيلي <span class="required">*</span>
                            </label>
                            <textarea class="form-control" id="address" name="address" rows="2"
                                      placeholder="اكتب العنوان التفصيلي للعقار..."
                                      required><?php echo htmlspecialchars($data['address'] ?? ''); ?></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="latitude" class="form-label">خط العرض (اختياري)</label>
                                    <input type="number" class="form-control" id="latitude" name="latitude" 
                                           placeholder="24.7136" step="any"
                                           value="<?php echo $data['latitude'] ?? ''; ?>">
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="longitude" class="form-label">خط الطول (اختياري)</label>
                                    <input type="number" class="form-control" id="longitude" name="longitude" 
                                           placeholder="46.6753" step="any"
                                           value="<?php echo $data['longitude'] ?? ''; ?>">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Images -->
                    <div class="form-section">
                        <h5 class="section-title">
                            <i class="bi bi-images me-2"></i>
                            صور العقار
                        </h5>
                        
                        <div class="image-upload-area" onclick="document.getElementById('images').click()">
                            <i class="bi bi-cloud-upload display-4 text-muted"></i>
                            <h5 class="mt-3">اسحب الصور هنا أو انقر للاختيار</h5>
                            <p class="text-muted">يمكنك رفع عدة صور (JPG, PNG, GIF) - الحد الأقصى 5 ميجابايت لكل صورة</p>
                            <input type="file" id="images" name="images[]" multiple accept="image/*" style="display: none;">
                        </div>
                        
                        <div id="imagePreview" class="image-preview"></div>
                    </div>
                    
                    <!-- Submit Buttons -->
                    <div class="form-section">
                        <div class="d-flex gap-3">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-check-circle me-2"></i>
                                حفظ العقار
                            </button>
                            <a href="index.php" class="btn btn-outline-secondary btn-lg">
                                <i class="bi bi-x-circle me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // معالجة رفع الصور
        const imageInput = document.getElementById('images');
        const imagePreview = document.getElementById('imagePreview');
        const uploadArea = document.querySelector('.image-upload-area');
        
        imageInput.addEventListener('change', handleImageSelect);
        
        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            imageInput.files = files;
            handleImageSelect();
        });
        
        function handleImageSelect() {
            const files = imageInput.files;
            imagePreview.innerHTML = '';
            
            if (files.length > 0) {
                imagePreview.style.display = 'block';
                
                Array.from(files).forEach((file, index) => {
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            const previewItem = document.createElement('div');
                            previewItem.className = 'preview-item';
                            previewItem.innerHTML = `
                                <img src="${e.target.result}" class="preview-image" alt="معاينة">
                                <button type="button" class="remove-image" onclick="removeImage(${index})">
                                    <i class="bi bi-x"></i>
                                </button>
                            `;
                            imagePreview.appendChild(previewItem);
                        };
                        reader.readAsDataURL(file);
                    }
                });
            } else {
                imagePreview.style.display = 'none';
            }
        }
        
        function removeImage(index) {
            const dt = new DataTransfer();
            const files = imageInput.files;
            
            for (let i = 0; i < files.length; i++) {
                if (i !== index) {
                    dt.items.add(files[i]);
                }
            }
            
            imageInput.files = dt.files;
            handleImageSelect();
        }
        
        // التحقق من صحة النموذج
        document.getElementById('propertyForm').addEventListener('submit', function(e) {
            const price = parseFloat(document.getElementById('price').value);
            
            if (price <= 0) {
                e.preventDefault();
                alert('السعر يجب أن يكون أكبر من صفر');
                return false;
            }
            
            // إظهار مؤشر التحميل
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري الحفظ...';
        });
        
        // تنسيق السعر
        document.getElementById('price').addEventListener('input', function() {
            const value = this.value.replace(/[^\d.]/g, '');
            this.value = value;
        });
        
        // الحصول على الموقع الحالي
        if (navigator.geolocation) {
            document.getElementById('getLocation') && document.getElementById('getLocation').addEventListener('click', function() {
                navigator.geolocation.getCurrentPosition(function(position) {
                    document.getElementById('latitude').value = position.coords.latitude;
                    document.getElementById('longitude').value = position.coords.longitude;
                });
            });
        }
    </script>
</body>
</html>
