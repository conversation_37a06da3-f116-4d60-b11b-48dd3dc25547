<?php
/**
 * صفحة التسجيل
 * Registration Page
 */

// التحقق من التثبيت
if (!file_exists('.installed')) {
    header('Location: install.php');
    exit;
}

require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// إعادة توجيه المستخدمين المسجلين
if (isLoggedIn()) {
    $user = getCurrentUser();
    if ($user['role'] === 'super_admin') {
        redirect('admin/dashboard.php');
    } else {
        redirect('user/dashboard.php');
    }
}

$error_message = '';
$success_message = '';

// معالجة التسجيل
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = [
        'username' => sanitize($_POST['username'] ?? ''),
        'email' => sanitize($_POST['email'] ?? ''),
        'password' => $_POST['password'] ?? '',
        'confirm_password' => $_POST['confirm_password'] ?? '',
        'full_name' => sanitize($_POST['full_name'] ?? ''),
        'phone' => sanitize($_POST['phone'] ?? ''),
        'company_name' => sanitize($_POST['company_name'] ?? '')
    ];
    
    // التحقق من البيانات
    if (empty($data['username']) || empty($data['email']) || empty($data['password']) || 
        empty($data['full_name']) || empty($data['company_name'])) {
        $error_message = 'جميع الحقول مطلوبة';
    } elseif ($data['password'] !== $data['confirm_password']) {
        $error_message = 'كلمة المرور وتأكيد كلمة المرور غير متطابقتين';
    } elseif (strlen($data['password']) < 6) {
        $error_message = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    } elseif (!isValidEmail($data['email'])) {
        $error_message = 'البريد الإلكتروني غير صحيح';
    } elseif (!empty($data['phone']) && !isValidSaudiPhone($data['phone'])) {
        $error_message = 'رقم الهاتف غير صحيح';
    } else {
        $result = $auth->register($data);
        
        if ($result['success']) {
            $success_message = 'تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول';
            // إعادة تعيين البيانات
            $data = [];
        } else {
            $error_message = $result['message'];
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .register-container {
            max-width: 500px;
            margin: 0 auto;
        }
        
        .register-card {
            background: white;
            border-radius: 20px;
            padding: 2.5rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        .register-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .register-logo {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .register-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .register-subtitle {
            color: #666;
            font-size: 0.9rem;
        }
        
        .form-floating {
            margin-bottom: 1rem;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-register {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 0.75rem;
            font-weight: 600;
            font-size: 1.1rem;
            width: 100%;
            margin-top: 1rem;
            transition: all 0.3s ease;
        }
        
        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            margin-bottom: 1rem;
        }
        
        .register-links {
            text-align: center;
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e9ecef;
        }
        
        .register-links a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .register-links a:hover {
            text-decoration: underline;
        }
        
        .back-home {
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .back-home:hover {
            color: rgba(255,255,255,0.8);
            transform: translateX(5px);
        }
        
        .password-strength {
            margin-top: 0.5rem;
            font-size: 0.8rem;
        }
        
        .strength-weak { color: #dc3545; }
        .strength-medium { color: #ffc107; }
        .strength-strong { color: #28a745; }
        
        .trial-info {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1.5rem;
            text-align: center;
        }
    </style>
</head>
<body>
    <a href="index.php" class="back-home">
        <i class="bi bi-arrow-right me-2"></i>
        العودة للرئيسية
    </a>
    
    <div class="container">
        <div class="register-container">
            <div class="register-card">
                <div class="register-header">
                    <div class="register-logo">
                        <i class="bi bi-building"></i>
                    </div>
                    <h1 class="register-title">إنشاء حساب جديد</h1>
                    <p class="register-subtitle">ابدأ رحلتك في إدارة العقارات</p>
                </div>
                
                <div class="trial-info">
                    <i class="bi bi-gift me-2"></i>
                    <strong>تجربة مجانية لمدة 30 يوم</strong>
                    <br>
                    <small>بدون الحاجة لبطاقة ائتمانية</small>
                </div>
                
                <?php if ($error_message): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success_message): ?>
                    <div class="alert alert-success" role="alert">
                        <i class="bi bi-check-circle me-2"></i>
                        <?php echo htmlspecialchars($success_message); ?>
                        <br>
                        <a href="login.php" class="alert-link">انقر هنا لتسجيل الدخول</a>
                    </div>
                <?php endif; ?>
                
                <form method="POST" id="registerForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="full_name" name="full_name" 
                                       placeholder="الاسم الكامل" required
                                       value="<?php echo htmlspecialchars($data['full_name'] ?? ''); ?>">
                                <label for="full_name">
                                    <i class="bi bi-person me-2"></i>
                                    الاسم الكامل
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="username" name="username" 
                                       placeholder="اسم المستخدم" required
                                       value="<?php echo htmlspecialchars($data['username'] ?? ''); ?>">
                                <label for="username">
                                    <i class="bi bi-at me-2"></i>
                                    اسم المستخدم
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-floating">
                        <input type="email" class="form-control" id="email" name="email" 
                               placeholder="البريد الإلكتروني" required
                               value="<?php echo htmlspecialchars($data['email'] ?? ''); ?>">
                        <label for="email">
                            <i class="bi bi-envelope me-2"></i>
                            البريد الإلكتروني
                        </label>
                    </div>
                    
                    <div class="form-floating">
                        <input type="tel" class="form-control" id="phone" name="phone" 
                               placeholder="رقم الهاتف (اختياري)"
                               value="<?php echo htmlspecialchars($data['phone'] ?? ''); ?>">
                        <label for="phone">
                            <i class="bi bi-phone me-2"></i>
                            رقم الهاتف (اختياري)
                        </label>
                    </div>
                    
                    <div class="form-floating">
                        <input type="text" class="form-control" id="company_name" name="company_name" 
                               placeholder="اسم الشركة/المكتب" required
                               value="<?php echo htmlspecialchars($data['company_name'] ?? ''); ?>">
                        <label for="company_name">
                            <i class="bi bi-building me-2"></i>
                            اسم الشركة/المكتب
                        </label>
                    </div>
                    
                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" name="password" 
                               placeholder="كلمة المرور" required>
                        <label for="password">
                            <i class="bi bi-lock me-2"></i>
                            كلمة المرور
                        </label>
                        <div id="passwordStrength" class="password-strength"></div>
                    </div>
                    
                    <div class="form-floating">
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                               placeholder="تأكيد كلمة المرور" required>
                        <label for="confirm_password">
                            <i class="bi bi-lock-fill me-2"></i>
                            تأكيد كلمة المرور
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-register">
                        <i class="bi bi-person-plus me-2"></i>
                        إنشاء الحساب
                    </button>
                </form>
                
                <div class="register-links">
                    <div>
                        لديك حساب بالفعل؟ 
                        <a href="login.php">تسجيل الدخول</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // التحقق من قوة كلمة المرور
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strengthDiv = document.getElementById('passwordStrength');
            
            let strength = 0;
            let message = '';
            
            if (password.length >= 6) strength++;
            if (password.match(/[a-z]/)) strength++;
            if (password.match(/[A-Z]/)) strength++;
            if (password.match(/[0-9]/)) strength++;
            if (password.match(/[^a-zA-Z0-9]/)) strength++;
            
            switch (strength) {
                case 0:
                case 1:
                    message = '<span class="strength-weak">ضعيفة جداً</span>';
                    break;
                case 2:
                    message = '<span class="strength-weak">ضعيفة</span>';
                    break;
                case 3:
                    message = '<span class="strength-medium">متوسطة</span>';
                    break;
                case 4:
                    message = '<span class="strength-strong">قوية</span>';
                    break;
                case 5:
                    message = '<span class="strength-strong">قوية جداً</span>';
                    break;
            }
            
            strengthDiv.innerHTML = password.length > 0 ? 'قوة كلمة المرور: ' + message : '';
        });
        
        // التحقق من تطابق كلمات المرور
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (confirmPassword.length > 0) {
                if (password === confirmPassword) {
                    this.setCustomValidity('');
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } else {
                    this.setCustomValidity('كلمات المرور غير متطابقة');
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                }
            } else {
                this.setCustomValidity('');
                this.classList.remove('is-valid', 'is-invalid');
            }
        });
        
        // تنسيق رقم الهاتف
        document.getElementById('phone').addEventListener('input', function() {
            let phone = this.value.replace(/\D/g, '');
            
            if (phone.startsWith('966')) {
                phone = '+' + phone;
            } else if (phone.startsWith('05')) {
                phone = '+966' + phone.substring(1);
            } else if (phone.startsWith('5') && phone.length === 9) {
                phone = '+966' + phone;
            }
            
            this.value = phone;
        });
        
        // التحقق من صحة البيانات قبل الإرسال
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('كلمات المرور غير متطابقة');
                return false;
            }
            
            if (password.length < 6) {
                e.preventDefault();
                alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                return false;
            }
        });
    </script>
</body>
</html>
