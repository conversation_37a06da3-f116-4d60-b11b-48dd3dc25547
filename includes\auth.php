<?php
/**
 * نظام المصادقة والتفويض
 * Authentication and Authorization System
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/functions.php';

class Auth {
    private $db;
    
    public function __construct() {
        global $database;
        $this->db = $database;
    }
    
    /**
     * تسجيل دخول المستخدم
     */
    public function login($username, $password, $remember = false) {
        try {
            // البحث عن المستخدم
            $sql = "SELECT u.*, c.name as company_name, c.status as company_status, c.subscription_expires 
                    FROM users u 
                    LEFT JOIN companies c ON u.company_id = c.id 
                    WHERE (u.username = ? OR u.email = ?) AND u.status = 'active'";
            
            $user = $this->db->fetch($sql, [$username, $username]);
            
            if (!$user) {
                return ['success' => false, 'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'];
            }
            
            // التحقق من كلمة المرور
            if (!verifyPassword($password, $user['password'])) {
                $this->logFailedLogin($username);
                return ['success' => false, 'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'];
            }
            
            // التحقق من حالة الشركة
            if ($user['company_status'] !== 'active') {
                return ['success' => false, 'message' => 'حساب الشركة غير نشط'];
            }
            
            // التحقق من انتهاء الاشتراك (تجاهل للحسابات التجريبية)
            if ($user['subscription_expires'] && $user['subscription_expires'] < date('Y-m-d') &&
                !in_array($user['email'], ['<EMAIL>', '<EMAIL>', '<EMAIL>'])) {
                return ['success' => false, 'message' => 'انتهت صلاحية الاشتراك'];
            }
            
            // إنشاء الجلسة
            $this->createSession($user);
            
            // تحديث آخر تسجيل دخول
            $this->updateLastLogin($user['id']);
            
            // حفظ cookie إذا طُلب ذلك
            if ($remember) {
                $this->setRememberCookie($user['id']);
            }
            
            return ['success' => true, 'message' => 'تم تسجيل الدخول بنجاح', 'user' => $user];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'حدث خطأ أثناء تسجيل الدخول'];
        }
    }
    
    /**
     * تسجيل خروج المستخدم
     */
    public function logout() {
        // حذف الجلسة
        session_unset();
        session_destroy();
        
        // حذف cookie التذكر
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/');
            setcookie('remember_user', '', time() - 3600, '/');
        }
        
        return true;
    }
    
    /**
     * تسجيل مستخدم جديد
     */
    public function register($data) {
        try {
            // التحقق من البيانات المطلوبة
            $required_fields = ['username', 'email', 'password', 'full_name', 'company_name'];
            foreach ($required_fields as $field) {
                if (empty($data[$field])) {
                    return ['success' => false, 'message' => 'جميع الحقول مطلوبة'];
                }
            }
            
            // التحقق من صحة البريد الإلكتروني
            if (!isValidEmail($data['email'])) {
                return ['success' => false, 'message' => 'البريد الإلكتروني غير صحيح'];
            }
            
            // التحقق من قوة كلمة المرور
            if (strlen($data['password']) < 6) {
                return ['success' => false, 'message' => 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'];
            }
            
            // التحقق من عدم وجود المستخدم مسبقاً
            if ($this->userExists($data['username'], $data['email'])) {
                return ['success' => false, 'message' => 'اسم المستخدم أو البريد الإلكتروني موجود مسبقاً'];
            }
            
            // بدء المعاملة
            $this->db->conn->beginTransaction();
            
            // إنشاء الشركة
            $company_data = [
                'name' => sanitize($data['company_name']),
                'email' => sanitize($data['email']),
                'phone' => isset($data['phone']) ? sanitize($data['phone']) : null,
                'subscription_plan' => 'basic',
                'subscription_expires' => date('Y-m-d', strtotime('+30 days')), // تجربة مجانية 30 يوم
                'status' => 'active'
            ];
            
            $company_id = $this->db->insert('companies', $company_data);
            
            // إنشاء المستخدم
            $user_data = [
                'company_id' => $company_id,
                'username' => sanitize($data['username']),
                'email' => sanitize($data['email']),
                'password' => hashPassword($data['password']),
                'full_name' => sanitize($data['full_name']),
                'phone' => isset($data['phone']) ? sanitize($data['phone']) : null,
                'role' => 'admin', // أول مستخدم يصبح مدير
                'status' => 'active'
            ];
            
            $user_id = $this->db->insert('users', $user_data);
            
            // إنشاء الإعدادات الافتراضية للشركة
            $this->createDefaultSettings($company_id);
            
            // تأكيد المعاملة
            $this->db->conn->commit();
            
            return ['success' => true, 'message' => 'تم إنشاء الحساب بنجاح', 'user_id' => $user_id];
            
        } catch (Exception $e) {
            // إلغاء المعاملة
            $this->db->conn->rollback();
            return ['success' => false, 'message' => 'حدث خطأ أثناء إنشاء الحساب'];
        }
    }
    
    /**
     * التحقق من وجود المستخدم
     */
    private function userExists($username, $email) {
        $sql = "SELECT id FROM users WHERE username = ? OR email = ?";
        $result = $this->db->fetch($sql, [$username, $email]);
        return $result !== false;
    }
    
    /**
     * إنشاء جلسة المستخدم
     */
    private function createSession($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['full_name'] = $user['full_name'];
        $_SESSION['email'] = $user['email'];
        $_SESSION['role'] = $user['role'];
        $_SESSION['company_id'] = $user['company_id'];
        $_SESSION['company_name'] = $user['company_name'];
        $_SESSION['login_time'] = time();
        
        // تجديد معرف الجلسة لمنع session fixation
        session_regenerate_id(true);
    }
    
    /**
     * تحديث آخر تسجيل دخول
     */
    private function updateLastLogin($user_id) {
        $sql = "UPDATE users SET last_login = NOW() WHERE id = ?";
        $this->db->query($sql, [$user_id]);
    }
    
    /**
     * تسجيل محاولة دخول فاشلة
     */
    private function logFailedLogin($username) {
        // يمكن إضافة نظام لتتبع محاولات الدخول الفاشلة هنا
        // وحظر IP بعد عدد معين من المحاولات
    }
    
    /**
     * إعداد cookie التذكر
     */
    private function setRememberCookie($user_id) {
        $token = generateToken();
        $expires = time() + (30 * 24 * 60 * 60); // 30 يوم
        
        // حفظ الرمز في قاعدة البيانات
        $sql = "UPDATE users SET remember_token = ? WHERE id = ?";
        $this->db->query($sql, [$token, $user_id]);
        
        // إعداد cookies
        setcookie('remember_token', $token, $expires, '/');
        setcookie('remember_user', $user_id, $expires, '/');
    }
    
    /**
     * التحقق من cookie التذكر
     */
    public function checkRememberCookie() {
        if (isset($_COOKIE['remember_token']) && isset($_COOKIE['remember_user'])) {
            $token = $_COOKIE['remember_token'];
            $user_id = $_COOKIE['remember_user'];
            
            $sql = "SELECT u.*, c.name as company_name, c.status as company_status 
                    FROM users u 
                    LEFT JOIN companies c ON u.company_id = c.id 
                    WHERE u.id = ? AND u.remember_token = ? AND u.status = 'active'";
            
            $user = $this->db->fetch($sql, [$user_id, $token]);
            
            if ($user && $user['company_status'] === 'active') {
                $this->createSession($user);
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * إنشاء الإعدادات الافتراضية للشركة
     */
    private function createDefaultSettings($company_id) {
        $default_settings = [
            'currency' => 'SAR',
            'language' => 'ar',
            'timezone' => 'Asia/Riyadh',
            'date_format' => 'd/m/Y',
            'records_per_page' => '20',
            'email_notifications' => '1',
            'sms_notifications' => '0'
        ];
        
        foreach ($default_settings as $key => $value) {
            setSetting($key, $value, $company_id);
        }
    }
    
    /**
     * التحقق من انتهاء الجلسة
     */
    public function checkSessionTimeout() {
        if (isset($_SESSION['login_time'])) {
            if (time() - $_SESSION['login_time'] > SESSION_TIMEOUT) {
                $this->logout();
                return false;
            }
            // تحديث وقت النشاط
            $_SESSION['login_time'] = time();
        }
        return true;
    }
    
    /**
     * التحقق من الصلاحيات
     */
    public function hasPermission($required_role) {
        if (!isLoggedIn()) {
            return false;
        }
        
        return hasPermission($required_role, $_SESSION['role']);
    }
    
    /**
     * إعادة تعيين كلمة المرور
     */
    public function resetPassword($email) {
        $user = $this->db->fetch("SELECT * FROM users WHERE email = ?", [$email]);
        
        if (!$user) {
            return ['success' => false, 'message' => 'البريد الإلكتروني غير موجود'];
        }
        
        // توليد رمز إعادة التعيين
        $reset_token = generateToken();
        $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));
        
        // حفظ الرمز في قاعدة البيانات
        $sql = "UPDATE users SET reset_token = ?, reset_expires = ? WHERE id = ?";
        $this->db->query($sql, [$reset_token, $expires, $user['id']]);
        
        // إرسال البريد الإلكتروني (يمكن تطويره لاحقاً)
        
        return ['success' => true, 'message' => 'تم إرسال رابط إعادة التعيين إلى بريدك الإلكتروني'];
    }
}

// إنشاء كائن المصادقة
$auth = new Auth();

// التحقق من cookie التذكر إذا لم يكن المستخدم مسجل دخول
if (!isLoggedIn()) {
    $auth->checkRememberCookie();
}

// التحقق من انتهاء الجلسة
if (isLoggedIn()) {
    $auth->checkSessionTimeout();
}
?>
