# نظام إدارة العقارات السحابي

URL: http://localhost/aq
البيانات التجريبية:
- <EMAIL> / password
- <EMAIL> / password

نظام شامل لإدارة مكاتب العقارات مبني بـ PHP خالص مع دعم كامل للغة العربية.

## 🌟 الميزات الرئيسية

### 📋 إدارة العقارات
- إضافة وتعديل وحذف العقارات
- رفع وإدارة صور متعددة للعقارات
- تصنيف العقارات (شقق، فلل، مكاتب، محلات، أراضي)
- تتبع حالة العقارات (متاح، مباع، مؤجر، قيد المراجعة)
- نظام بحث وفلترة متقدم
- عداد المشاهدات

### 👥 إدارة العملاء
- تصنيف العملاء (مشتري، بائع، مستأجر، مؤجر)
- حفظ معلومات الاتصال والهوية
- تتبع تاريخ المعاملات لكل عميل
- نظام بحث في بيانات العملاء

### 💰 إدارة المعاملات
- تسجيل معاملات البيع والإيجار
- حساب العمولات تلقائياً
- تتبع حالة المعاملات (معلقة، مكتملة، ملغية)
- ربط المعاملات بالعقارات والعملاء

### 📊 التقارير والإحصائيات
- تقارير مالية شاملة
- إحصائيات العقارات والعملاء
- رسوم بيانية تفاعلية
- تصدير التقارير (PDF, Excel)
- تحليل الأداء الشهري

### 🏢 نظام متعدد المستأجرين
- دعم عدة شركات في نفس النظام
- عزل بيانات كل شركة
- خطط اشتراك مختلفة
- إدارة المستخدمين والصلاحيات

### 🔐 الأمان والحماية
- تشفير كلمات المرور
- نظام جلسات آمن
- حماية من SQL Injection
- تحقق من صحة البيانات
- نظام صلاحيات متدرج

## 🛠️ التقنيات المستخدمة

- **Backend**: PHP 8.0+ (خالص بدون إطار عمل)
- **Database**: MySQL 8.0+
- **Frontend**: HTML5, CSS3, JavaScript
- **UI Framework**: Bootstrap 5.3
- **Icons**: Bootstrap Icons
- **Charts**: Chart.js
- **Fonts**: Google Fonts (Cairo)

## 📋 متطلبات النظام

- PHP 8.0 أو أحدث
- MySQL 8.0 أو أحدث
- Apache/Nginx Web Server
- مساحة تخزين 500MB على الأقل
- دعم GD Library لمعالجة الصور

## 🚀 التثبيت والإعداد

### 1. تحميل الملفات
```bash
git clone https://github.com/your-repo/real-estate-saas.git
cd real-estate-saas
```

### 2. إعداد قاعدة البيانات
1. أنشئ قاعدة بيانات جديدة في MySQL
2. استورد ملف `database.sql`
3. عدّل إعدادات الاتصال في `config/database.php`

### 3. إعداد الخادم
1. انسخ الملفات إلى مجلد الويب
2. تأكد من صلاحيات الكتابة على مجلد `uploads/`
3. قم بتشغيل الخادم

### 4. الوصول للنظام
- افتح المتصفح وانتقل إلى عنوان الموقع
- سجل حساب جديد أو استخدم البيانات التجريبية

## 👤 البيانات التجريبية

### حسابات المدراء:
- **البريد**: <EMAIL>
- **كلمة المرور**: password

- **البريد**: <EMAIL>  
- **كلمة المرور**: password

## 📁 هيكل المشروع

```
real-estate-saas/
├── config/                 # ملفات الإعدادات
│   ├── config.php          # الإعدادات العامة
│   └── database.php        # إعدادات قاعدة البيانات
├── includes/               # الملفات المشتركة
│   ├── functions.php       # الدوال المساعدة
│   └── auth.php           # نظام المصادقة
├── user/                   # لوحة تحكم المستخدم
│   ├── dashboard.php       # الصفحة الرئيسية
│   ├── properties/         # إدارة العقارات
│   ├── clients/           # إدارة العملاء
│   ├── transactions/      # إدارة المعاملات
│   └── reports/           # التقارير
├── admin/                  # لوحة تحكم المدير العام
├── assets/                 # الملفات الثابتة
│   ├── css/               # ملفات الأنماط
│   ├── js/                # ملفات JavaScript
│   └── images/            # الصور
├── uploads/               # ملفات المستخدمين
│   └── properties/        # صور العقارات
├── index.php              # الصفحة الرئيسية
├── login.php              # تسجيل الدخول
├── register.php           # التسجيل
└── database.sql           # هيكل قاعدة البيانات
```

## 🎨 التخصيص

### تغيير الألوان
عدّل المتغيرات في `assets/css/style.css`:
```css
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    /* ... */
}
```

### إضافة لغات جديدة
1. أنشئ ملف ترجمة في `includes/languages/`
2. عدّل `config/config.php` لإضافة اللغة الجديدة

### تخصيص التقارير
عدّل ملفات التقارير في `user/reports/` لإضافة تقارير مخصصة

## 🔧 الصيانة

### النسخ الاحتياطي
```bash
# نسخ احتياطي لقاعدة البيانات
mysqldump -u username -p database_name > backup.sql

# نسخ احتياطي للملفات
tar -czf backup.tar.gz /path/to/project
```

### التحديثات
1. احتفظ بنسخة احتياطية
2. حمّل الإصدار الجديد
3. شغّل سكريبت التحديث إن وُجد

## 🐛 استكشاف الأخطاء

### مشاكل شائعة:

**خطأ في الاتصال بقاعدة البيانات:**
- تحقق من إعدادات `config/database.php`
- تأكد من تشغيل خدمة MySQL

**مشاكل رفع الصور:**
- تحقق من صلاحيات مجلد `uploads/`
- تأكد من إعدادات PHP للرفع

**مشاكل الجلسات:**
- تحقق من إعدادات session في PHP
- امسح ملفات الجلسات المؤقتة

## 📞 الدعم الفني

- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: https://realestate-saas.com
- **التوثيق**: https://docs.realestate-saas.com

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

## 🙏 شكر وتقدير

- [Bootstrap](https://getbootstrap.com/) للواجهات
- [Chart.js](https://www.chartjs.org/) للرسوم البيانية
- [Bootstrap Icons](https://icons.getbootstrap.com/) للأيقونات
- [Picsum Photos](https://picsum.photos/) للصور التجريبية

---

**تم تطوير هذا النظام بـ ❤️ لخدمة مكاتب العقارات في المنطقة العربية**
