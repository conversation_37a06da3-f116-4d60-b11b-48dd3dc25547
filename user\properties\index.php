<?php
/**
 * صفحة إدارة العقارات
 * Properties Management Page
 */

require_once '../../config/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../../login.php');
}

$user = getCurrentUser();
$company_id = $user['company_id'];

// معالجة البحث والفلترة
$search = sanitize($_GET['search'] ?? '');
$status_filter = sanitize($_GET['status'] ?? '');
$type_filter = sanitize($_GET['type'] ?? '');
$transaction_filter = sanitize($_GET['transaction'] ?? '');
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = RECORDS_PER_PAGE;
$offset = ($page - 1) * $per_page;

// بناء استعلام البحث
$where_conditions = ['company_id = ?'];
$params = [$company_id];

if (!empty($search)) {
    $where_conditions[] = '(title LIKE ? OR description LIKE ? OR address LIKE ? OR city LIKE ?)';
    $search_term = "%{$search}%";
    $params = array_merge($params, [$search_term, $search_term, $search_term, $search_term]);
}

if (!empty($status_filter)) {
    $where_conditions[] = 'status = ?';
    $params[] = $status_filter;
}

if (!empty($type_filter)) {
    $where_conditions[] = 'property_type = ?';
    $params[] = $type_filter;
}

if (!empty($transaction_filter)) {
    $where_conditions[] = 'transaction_type = ?';
    $params[] = $transaction_filter;
}

$where_clause = implode(' AND ', $where_conditions);

try {
    // عد إجمالي العقارات
    $total_properties = $database->count('properties', $where_clause, $params);
    
    // جلب العقارات مع الترقيم
    $sql = "SELECT p.*, 
                   (SELECT image_path FROM property_images WHERE property_id = p.id AND is_primary = 1 LIMIT 1) as primary_image,
                   (SELECT COUNT(*) FROM property_images WHERE property_id = p.id) as images_count
            FROM properties p 
            WHERE {$where_clause} 
            ORDER BY p.created_at DESC 
            LIMIT {$per_page} OFFSET {$offset}";
    
    $properties = $database->fetchAll($sql, $params);
    
    // حساب عدد الصفحات
    $total_pages = ceil($total_properties / $per_page);
    
} catch (Exception $e) {
    $error_message = "حدث خطأ في جلب البيانات";
    $properties = [];
    $total_properties = 0;
    $total_pages = 0;
}

// معالجة حذف العقار
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_property'])) {
    $property_id = intval($_POST['property_id']);
    
    try {
        // التحقق من ملكية العقار
        $property = $database->fetch("SELECT * FROM properties WHERE id = ? AND company_id = ?", [$property_id, $company_id]);
        
        if ($property) {
            // حذف صور العقار
            $images = $database->fetchAll("SELECT image_path FROM property_images WHERE property_id = ?", [$property_id]);
            foreach ($images as $image) {
                $image_path = "../../uploads/properties/" . $image['image_path'];
                if (file_exists($image_path)) {
                    unlink($image_path);
                }
            }
            
            // حذف العقار (سيتم حذف الصور تلقائياً بسبب CASCADE)
            $database->delete('properties', 'id = ?', [$property_id]);
            
            setAlert('تم حذف العقار بنجاح', 'success');
        } else {
            setAlert('العقار غير موجود', 'error');
        }
    } catch (Exception $e) {
        setAlert('حدث خطأ أثناء حذف العقار', 'error');
    }
    
    redirect('index.php');
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العقارات - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <style>
        .property-card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .property-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        
        .property-image {
            height: 200px;
            object-fit: cover;
            border-radius: 10px 10px 0 0;
        }
        
        .property-status {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 2;
        }
        
        .property-price {
            font-size: 1.2rem;
            font-weight: 700;
            color: #667eea;
        }
        
        .property-details {
            font-size: 0.9rem;
            color: #666;
        }
        
        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .search-box {
            position: relative;
        }
        
        .search-box .form-control {
            padding-right: 3rem;
        }
        
        .search-box .search-icon {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }
        
        .stats-summary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2>إدارة العقارات</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="../dashboard.php">لوحة التحكم</a></li>
                                <li class="breadcrumb-item active">العقارات</li>
                            </ol>
                        </nav>
                    </div>
                    <a href="add.php" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>
                        إضافة عقار جديد
                    </a>
                </div>
                
                <!-- Alerts -->
                <?php showAlert(); ?>
                
                <!-- Stats Summary -->
                <div class="stats-summary">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <h3><?php echo formatNumber($total_properties); ?></h3>
                            <small>إجمالي العقارات</small>
                        </div>
                        <div class="col-md-3">
                            <h3><?php echo formatNumber($database->count('properties', 'company_id = ? AND status = ?', [$company_id, 'available'])); ?></h3>
                            <small>متاح</small>
                        </div>
                        <div class="col-md-3">
                            <h3><?php echo formatNumber($database->count('properties', 'company_id = ? AND status = ?', [$company_id, 'sold'])); ?></h3>
                            <small>مباع</small>
                        </div>
                        <div class="col-md-3">
                            <h3><?php echo formatNumber($database->count('properties', 'company_id = ? AND status = ?', [$company_id, 'rented'])); ?></h3>
                            <small>مؤجر</small>
                        </div>
                    </div>
                </div>
                
                <!-- Filters -->
                <div class="filter-section">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <div class="search-box">
                                <input type="text" class="form-control" name="search" 
                                       placeholder="البحث في العقارات..." 
                                       value="<?php echo htmlspecialchars($search); ?>">
                                <i class="bi bi-search search-icon"></i>
                            </div>
                        </div>
                        
                        <div class="col-md-2">
                            <select name="status" class="form-select">
                                <option value="">جميع الحالات</option>
                                <?php foreach (PROPERTY_STATUS as $key => $value): ?>
                                    <option value="<?php echo $key; ?>" <?php echo $status_filter === $key ? 'selected' : ''; ?>>
                                        <?php echo $value; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <select name="type" class="form-select">
                                <option value="">جميع الأنواع</option>
                                <?php foreach (PROPERTY_TYPES as $key => $value): ?>
                                    <option value="<?php echo $key; ?>" <?php echo $type_filter === $key ? 'selected' : ''; ?>>
                                        <?php echo $value; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <select name="transaction" class="form-select">
                                <option value="">جميع المعاملات</option>
                                <?php foreach (TRANSACTION_TYPES as $key => $value): ?>
                                    <option value="<?php echo $key; ?>" <?php echo $transaction_filter === $key ? 'selected' : ''; ?>>
                                        <?php echo $value; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="bi bi-funnel me-2"></i>
                                فلترة
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- Properties Grid -->
                <?php if (empty($properties)): ?>
                    <div class="text-center py-5">
                        <i class="bi bi-building display-1 text-muted"></i>
                        <h4 class="mt-3">لا توجد عقارات</h4>
                        <p class="text-muted">لم يتم العثور على عقارات تطابق معايير البحث</p>
                        <a href="add.php" class="btn btn-primary">إضافة عقار جديد</a>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($properties as $property): ?>
                            <div class="col-lg-4 col-md-6 mb-4">
                                <div class="card property-card h-100">
                                    <div class="position-relative">
                                        <?php if ($property['primary_image']): ?>
                                            <img src="../../uploads/properties/<?php echo $property['primary_image']; ?>" 
                                                 class="card-img-top property-image" 
                                                 alt="<?php echo htmlspecialchars($property['title']); ?>">
                                        <?php else: ?>
                                            <img src="<?php echo generateImagePlaceholder(400, 200, $property['id']); ?>" 
                                                 class="card-img-top property-image" 
                                                 alt="<?php echo htmlspecialchars($property['title']); ?>">
                                        <?php endif; ?>
                                        
                                        <div class="property-status">
                                            <?php
                                            $status_class = '';
                                            switch ($property['status']) {
                                                case 'available': $status_class = 'bg-success'; break;
                                                case 'sold': $status_class = 'bg-danger'; break;
                                                case 'rented': $status_class = 'bg-warning'; break;
                                                case 'pending': $status_class = 'bg-info'; break;
                                            }
                                            ?>
                                            <span class="badge <?php echo $status_class; ?>">
                                                <?php echo PROPERTY_STATUS[$property['status']]; ?>
                                            </span>
                                        </div>
                                        
                                        <?php if ($property['images_count'] > 1): ?>
                                            <div class="position-absolute bottom-0 start-0 m-2">
                                                <span class="badge bg-dark">
                                                    <i class="bi bi-images me-1"></i>
                                                    <?php echo $property['images_count']; ?>
                                                </span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="card-body d-flex flex-column">
                                        <h5 class="card-title"><?php echo htmlspecialchars($property['title']); ?></h5>
                                        
                                        <div class="property-details mb-2">
                                            <small>
                                                <i class="bi bi-geo-alt me-1"></i>
                                                <?php echo htmlspecialchars($property['city']); ?>
                                                <?php if ($property['district']): ?>
                                                    - <?php echo htmlspecialchars($property['district']); ?>
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                        
                                        <div class="property-details mb-2">
                                            <span class="badge bg-light text-dark me-2">
                                                <?php echo PROPERTY_TYPES[$property['property_type']]; ?>
                                            </span>
                                            <span class="badge bg-light text-dark">
                                                <?php echo TRANSACTION_TYPES[$property['transaction_type']]; ?>
                                            </span>
                                        </div>
                                        
                                        <?php if ($property['area']): ?>
                                            <div class="property-details mb-2">
                                                <i class="bi bi-rulers me-1"></i>
                                                <?php echo formatArea($property['area']); ?>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if ($property['bedrooms'] || $property['bathrooms']): ?>
                                            <div class="property-details mb-2">
                                                <?php if ($property['bedrooms']): ?>
                                                    <i class="bi bi-door-closed me-1"></i>
                                                    <?php echo $property['bedrooms']; ?> غرف
                                                <?php endif; ?>
                                                
                                                <?php if ($property['bathrooms']): ?>
                                                    <i class="bi bi-droplet me-1 ms-2"></i>
                                                    <?php echo $property['bathrooms']; ?> حمام
                                                <?php endif; ?>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="property-price mb-3">
                                            <?php echo formatCurrency($property['price']); ?>
                                        </div>
                                        
                                        <div class="mt-auto">
                                            <div class="btn-group w-100" role="group">
                                                <a href="view.php?id=<?php echo $property['id']; ?>" 
                                                   class="btn btn-outline-primary btn-sm">
                                                    <i class="bi bi-eye"></i>
                                                    عرض
                                                </a>
                                                <a href="edit.php?id=<?php echo $property['id']; ?>" 
                                                   class="btn btn-outline-secondary btn-sm">
                                                    <i class="bi bi-pencil"></i>
                                                    تعديل
                                                </a>
                                                <button type="button" 
                                                        class="btn btn-outline-danger btn-sm"
                                                        onclick="deleteProperty(<?php echo $property['id']; ?>, '<?php echo htmlspecialchars($property['title']); ?>')">
                                                    <i class="bi bi-trash"></i>
                                                    حذف
                                                </button>
                                            </div>
                                        </div>
                                        
                                        <div class="text-muted small mt-2">
                                            <i class="bi bi-calendar me-1"></i>
                                            <?php echo formatDate($property['created_at'], 'arabic_date'); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                        <nav aria-label="تنقل الصفحات">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page - 1; ?>&<?php echo http_build_query($_GET); ?>">
                                            السابق
                                        </a>
                                    </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>&<?php echo http_build_query($_GET); ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>
                                
                                <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page + 1; ?>&<?php echo http_build_query($_GET); ?>">
                                            التالي
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف العقار "<span id="propertyTitle"></span>"؟</p>
                    <p class="text-danger small">هذا الإجراء لا يمكن التراجع عنه.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="property_id" id="deletePropertyId">
                        <button type="submit" name="delete_property" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function deleteProperty(id, title) {
            document.getElementById('deletePropertyId').value = id;
            document.getElementById('propertyTitle').textContent = title;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
        
        // تحسين تجربة البحث
        let searchTimeout;
        document.querySelector('input[name="search"]').addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.form.submit();
            }, 500);
        });
    </script>
</body>
</html>
