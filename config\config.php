<?php
/**
 * الإعدادات العامة للنظام
 * General System Configuration
 */

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// إعدادات النظام الأساسية
define('SITE_NAME', 'نظام إدارة العقارات');
define('SITE_URL', 'http://localhost/aq');
define('ADMIN_EMAIL', '<EMAIL>');
define('DEFAULT_LANGUAGE', 'ar');
define('TIMEZONE', 'Asia/Riyadh');

// إعدادات الأمان
define('HASH_ALGO', 'sha256');
define('SESSION_TIMEOUT', 3600); // ساعة واحدة
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_TIMEOUT', 900); // 15 دقيقة

// إعدادات رفع الملفات
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5 ميجابايت
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('ALLOWED_DOCUMENT_TYPES', ['pdf', 'doc', 'docx', 'txt']);

// إعدادات الصور
define('IMAGE_QUALITY', 85);
define('THUMBNAIL_WIDTH', 300);
define('THUMBNAIL_HEIGHT', 200);
define('LARGE_IMAGE_WIDTH', 1200);
define('LARGE_IMAGE_HEIGHT', 800);

// إعدادات الترقيم
define('RECORDS_PER_PAGE', 20);

// إعدادات البريد الإلكتروني
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('SMTP_ENCRYPTION', 'tls');

// إعدادات العملة
define('CURRENCY_SYMBOL', 'ر.س');
define('CURRENCY_CODE', 'SAR');

// مستويات المستخدمين
define('USER_ROLES', [
    'super_admin' => 'مدير عام',
    'admin' => 'مدير',
    'agent' => 'وسيط عقاري',
    'user' => 'مستخدم'
]);

// حالات العقارات
define('PROPERTY_STATUS', [
    'available' => 'متاح',
    'sold' => 'مباع',
    'rented' => 'مؤجر',
    'pending' => 'قيد المراجعة'
]);

// أنواع العقارات
define('PROPERTY_TYPES', [
    'apartment' => 'شقة',
    'villa' => 'فيلا',
    'office' => 'مكتب',
    'shop' => 'محل تجاري',
    'land' => 'أرض',
    'warehouse' => 'مستودع'
]);

// أنواع المعاملات
define('TRANSACTION_TYPES', [
    'sale' => 'بيع',
    'rent' => 'إيجار'
]);

// أنواع العملاء
define('CLIENT_TYPES', [
    'buyer' => 'مشتري',
    'seller' => 'بائع',
    'renter' => 'مستأجر',
    'landlord' => 'مؤجر'
]);

// خطط الاشتراك
define('SUBSCRIPTION_PLANS', [
    'basic' => [
        'name' => 'أساسي',
        'price' => 99,
        'properties_limit' => 50,
        'users_limit' => 3,
        'features' => ['إدارة العقارات', 'إدارة العملاء', 'التقارير الأساسية']
    ],
    'premium' => [
        'name' => 'متقدم',
        'price' => 199,
        'properties_limit' => 200,
        'users_limit' => 10,
        'features' => ['جميع ميزات الأساسي', 'التقارير المتقدمة', 'API', 'دعم فني']
    ],
    'enterprise' => [
        'name' => 'مؤسسي',
        'price' => 399,
        'properties_limit' => -1, // غير محدود
        'users_limit' => -1, // غير محدود
        'features' => ['جميع الميزات', 'تخصيص كامل', 'دعم فني 24/7', 'تدريب']
    ]
]);

// تعيين المنطقة الزمنية
date_default_timezone_set(TIMEZONE);

// إعدادات العرض
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// تضمين ملفات الإعدادات الأخرى
require_once __DIR__ . '/database.php';

/**
 * دالة للحصول على الإعدادات من قاعدة البيانات
 */
function getSetting($key, $company_id = null, $default = null) {
    global $database;
    
    $sql = "SELECT setting_value FROM settings WHERE setting_key = ?";
    $params = [$key];
    
    if ($company_id) {
        $sql .= " AND company_id = ?";
        $params[] = $company_id;
    } else {
        $sql .= " AND company_id IS NULL";
    }
    
    $result = $database->fetch($sql, $params);
    return $result ? $result['setting_value'] : $default;
}

/**
 * دالة لحفظ الإعدادات في قاعدة البيانات
 */
function setSetting($key, $value, $company_id = null) {
    global $database;
    
    $data = [
        'setting_key' => $key,
        'setting_value' => $value,
        'company_id' => $company_id
    ];
    
    // التحقق من وجود الإعداد
    $sql = "SELECT id FROM settings WHERE setting_key = ?";
    $params = [$key];
    
    if ($company_id) {
        $sql .= " AND company_id = ?";
        $params[] = $company_id;
    } else {
        $sql .= " AND company_id IS NULL";
    }
    
    $existing = $database->fetch($sql, $params);
    
    if ($existing) {
        // تحديث الإعداد الموجود
        $database->update('settings', 
            ['setting_value' => $value], 
            'id = ?', 
            [$existing['id']]
        );
    } else {
        // إدراج إعداد جديد
        $database->insert('settings', $data);
    }
}

/**
 * دالة للتحقق من صحة البريد الإلكتروني
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * دالة لتنظيف البيانات
 */
function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

/**
 * دالة لتوليد رمز عشوائي
 */
function generateToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}
?>
