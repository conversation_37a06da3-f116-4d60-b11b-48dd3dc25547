<?php
/**
 * الدوال المساعدة
 * Helper Functions
 */

/**
 * دالة لتشفير كلمة المرور
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * دالة للتحقق من كلمة المرور
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * دالة لتوليد رقم عشوائي للصور
 */
function generateImagePlaceholder($width = 400, $height = 300, $seed = null) {
    if ($seed === null) {
        $seed = rand(1, 1000);
    }
    return "https://picsum.photos/{$width}/{$height}?random={$seed}";
}

/**
 * دالة لتنسيق التاريخ
 */
function formatDate($date, $format = 'Y-m-d H:i:s') {
    if (empty($date)) return '';
    
    $dateObj = new DateTime($date);
    
    // تنسيقات مختلفة حسب اللغة
    switch ($format) {
        case 'arabic_date':
            return $dateObj->format('d/m/Y');
        case 'arabic_datetime':
            return $dateObj->format('d/m/Y H:i');
        case 'relative':
            return getRelativeTime($date);
        default:
            return $dateObj->format($format);
    }
}

/**
 * دالة لحساب الوقت النسبي
 */
function getRelativeTime($date) {
    $now = new DateTime();
    $past = new DateTime($date);
    $diff = $now->diff($past);
    
    if ($diff->days > 0) {
        return $diff->days . ' يوم مضى';
    } elseif ($diff->h > 0) {
        return $diff->h . ' ساعة مضت';
    } elseif ($diff->i > 0) {
        return $diff->i . ' دقيقة مضت';
    } else {
        return 'الآن';
    }
}

/**
 * دالة لتنسيق الأرقام
 */
function formatNumber($number, $decimals = 0) {
    return number_format($number, $decimals, '.', ',');
}

/**
 * دالة لتنسيق العملة
 */
function formatCurrency($amount, $currency = CURRENCY_SYMBOL) {
    return formatNumber($amount, 2) . ' ' . $currency;
}

/**
 * دالة لتنسيق المساحة
 */
function formatArea($area) {
    return formatNumber($area, 1) . ' م²';
}

/**
 * دالة للحصول على الصفحة الحالية
 */
function getCurrentPage() {
    return basename($_SERVER['PHP_SELF'], '.php');
}

/**
 * دالة للتحقق من صلاحية المستخدم
 */
function hasPermission($required_role, $user_role) {
    $roles_hierarchy = [
        'user' => 1,
        'agent' => 2,
        'admin' => 3,
        'super_admin' => 4
    ];
    
    return $roles_hierarchy[$user_role] >= $roles_hierarchy[$required_role];
}

/**
 * دالة لإنشاء رابط آمن
 */
function createSecureLink($page, $params = []) {
    $url = SITE_URL . '/' . $page;
    
    if (!empty($params)) {
        $url .= '?' . http_build_query($params);
    }
    
    return $url;
}

/**
 * دالة لرفع الملفات
 */
function uploadFile($file, $destination_folder, $allowed_types = ALLOWED_IMAGE_TYPES) {
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        return ['success' => false, 'message' => 'لم يتم اختيار ملف'];
    }
    
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    // التحقق من نوع الملف
    if (!in_array($file_extension, $allowed_types)) {
        return ['success' => false, 'message' => 'نوع الملف غير مسموح'];
    }
    
    // التحقق من حجم الملف
    if ($file['size'] > MAX_FILE_SIZE) {
        return ['success' => false, 'message' => 'حجم الملف كبير جداً'];
    }
    
    // إنشاء اسم ملف فريد
    $new_filename = uniqid() . '_' . time() . '.' . $file_extension;
    $destination_path = $destination_folder . '/' . $new_filename;
    
    // إنشاء المجلد إذا لم يكن موجوداً
    if (!file_exists($destination_folder)) {
        mkdir($destination_folder, 0755, true);
    }
    
    // رفع الملف
    if (move_uploaded_file($file['tmp_name'], $destination_path)) {
        return ['success' => true, 'filename' => $new_filename, 'path' => $destination_path];
    } else {
        return ['success' => false, 'message' => 'فشل في رفع الملف'];
    }
}

/**
 * دالة لتغيير حجم الصورة
 */
function resizeImage($source_path, $destination_path, $max_width, $max_height, $quality = IMAGE_QUALITY) {
    $image_info = getimagesize($source_path);
    if (!$image_info) {
        return false;
    }
    
    $original_width = $image_info[0];
    $original_height = $image_info[1];
    $image_type = $image_info[2];
    
    // حساب الأبعاد الجديدة
    $ratio = min($max_width / $original_width, $max_height / $original_height);
    $new_width = round($original_width * $ratio);
    $new_height = round($original_height * $ratio);
    
    // إنشاء الصورة المصدر
    switch ($image_type) {
        case IMAGETYPE_JPEG:
            $source_image = imagecreatefromjpeg($source_path);
            break;
        case IMAGETYPE_PNG:
            $source_image = imagecreatefrompng($source_path);
            break;
        case IMAGETYPE_GIF:
            $source_image = imagecreatefromgif($source_path);
            break;
        default:
            return false;
    }
    
    // إنشاء الصورة الجديدة
    $new_image = imagecreatetruecolor($new_width, $new_height);
    
    // الحفاظ على الشفافية للـ PNG
    if ($image_type == IMAGETYPE_PNG) {
        imagealphablending($new_image, false);
        imagesavealpha($new_image, true);
    }
    
    // تغيير حجم الصورة
    imagecopyresampled($new_image, $source_image, 0, 0, 0, 0, $new_width, $new_height, $original_width, $original_height);
    
    // حفظ الصورة الجديدة
    $result = false;
    switch ($image_type) {
        case IMAGETYPE_JPEG:
            $result = imagejpeg($new_image, $destination_path, $quality);
            break;
        case IMAGETYPE_PNG:
            $result = imagepng($new_image, $destination_path);
            break;
        case IMAGETYPE_GIF:
            $result = imagegif($new_image, $destination_path);
            break;
    }
    
    // تنظيف الذاكرة
    imagedestroy($source_image);
    imagedestroy($new_image);
    
    return $result;
}

/**
 * دالة لإنشاء رسالة تنبيه
 */
function setAlert($message, $type = 'info') {
    $_SESSION['alert'] = [
        'message' => $message,
        'type' => $type
    ];
}

/**
 * دالة لعرض رسالة التنبيه
 */
function showAlert() {
    if (isset($_SESSION['alert'])) {
        $alert = $_SESSION['alert'];
        $class = '';
        
        switch ($alert['type']) {
            case 'success':
                $class = 'alert-success';
                break;
            case 'error':
                $class = 'alert-danger';
                break;
            case 'warning':
                $class = 'alert-warning';
                break;
            default:
                $class = 'alert-info';
        }
        
        echo '<div class="alert ' . $class . ' alert-dismissible fade show" role="alert">';
        echo htmlspecialchars($alert['message']);
        echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
        echo '</div>';
        
        unset($_SESSION['alert']);
    }
}

/**
 * دالة للتحقق من تسجيل الدخول
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * دالة للحصول على معلومات المستخدم الحالي
 */
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    global $database;
    
    $sql = "SELECT u.*, c.name as company_name, c.subscription_plan 
            FROM users u 
            LEFT JOIN companies c ON u.company_id = c.id 
            WHERE u.id = ?";
    
    return $database->fetch($sql, [$_SESSION['user_id']]);
}

/**
 * دالة لإعادة التوجيه
 */
function redirect($url) {
    header("Location: " . $url);
    exit();
}

/**
 * دالة للتحقق من صحة رقم الهاتف السعودي
 */
function isValidSaudiPhone($phone) {
    // إزالة المسافات والرموز
    $phone = preg_replace('/[^0-9+]/', '', $phone);
    
    // التحقق من الأنماط المختلفة
    $patterns = [
        '/^(\+966|966|0)?5[0-9]{8}$/',  // أرقام الجوال
        '/^(\+966|966|0)?1[1-9][0-9]{7}$/'  // أرقام الأرضي
    ];
    
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $phone)) {
            return true;
        }
    }
    
    return false;
}

/**
 * دالة لتنسيق رقم الهاتف
 */
function formatPhone($phone) {
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    if (strlen($phone) == 10 && substr($phone, 0, 1) == '5') {
        return '+966' . $phone;
    } elseif (strlen($phone) == 9 && substr($phone, 0, 1) == '5') {
        return '+966' . $phone;
    }
    
    return $phone;
}
?>
