<?php
/**
 * صفحة التقارير الرئيسية
 * Main Reports Page
 */

require_once '../../config/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../../login.php');
}

$user = getCurrentUser();
$company_id = $user['company_id'];

// تحديد الفترة الزمنية (آخر 30 يوم افتراضياً)
$date_from = sanitize($_GET['date_from'] ?? date('Y-m-d', strtotime('-30 days')));
$date_to = sanitize($_GET['date_to'] ?? date('Y-m-d'));

try {
    // إحصائيات العقارات
    $properties_stats = [
        'total' => $database->count('properties', 'company_id = ?', [$company_id]),
        'available' => $database->count('properties', 'company_id = ? AND status = ?', [$company_id, 'available']),
        'sold' => $database->count('properties', 'company_id = ? AND status = ?', [$company_id, 'sold']),
        'rented' => $database->count('properties', 'company_id = ? AND status = ?', [$company_id, 'rented']),
        'pending' => $database->count('properties', 'company_id = ? AND status = ?', [$company_id, 'pending'])
    ];
    
    // إحصائيات العملاء
    $clients_stats = [
        'total' => $database->count('clients', 'company_id = ?', [$company_id]),
        'buyers' => $database->count('clients', 'company_id = ? AND client_type = ?', [$company_id, 'buyer']),
        'sellers' => $database->count('clients', 'company_id = ? AND client_type = ?', [$company_id, 'seller']),
        'renters' => $database->count('clients', 'company_id = ? AND client_type = ?', [$company_id, 'renter']),
        'landlords' => $database->count('clients', 'company_id = ? AND client_type = ?', [$company_id, 'landlord'])
    ];
    
    // إحصائيات المعاملات للفترة المحددة
    $transactions_stats = [
        'total' => $database->count('transactions', 'company_id = ? AND DATE(created_at) BETWEEN ? AND ?', [$company_id, $date_from, $date_to]),
        'completed' => $database->count('transactions', 'company_id = ? AND status = ? AND DATE(created_at) BETWEEN ? AND ?', [$company_id, 'completed', $date_from, $date_to]),
        'pending' => $database->count('transactions', 'company_id = ? AND status = ? AND DATE(created_at) BETWEEN ? AND ?', [$company_id, 'pending', $date_from, $date_to]),
        'cancelled' => $database->count('transactions', 'company_id = ? AND status = ? AND DATE(created_at) BETWEEN ? AND ?', [$company_id, 'cancelled', $date_from, $date_to])
    ];
    
    // إحصائيات مالية للفترة المحددة
    $financial_stats = $database->fetch(
        "SELECT 
            SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_sales,
            SUM(CASE WHEN status = 'completed' THEN commission ELSE 0 END) as total_commission,
            AVG(CASE WHEN status = 'completed' THEN amount ELSE NULL END) as avg_transaction,
            SUM(CASE WHEN status = 'completed' AND transaction_type = 'sale' THEN amount ELSE 0 END) as sales_revenue,
            SUM(CASE WHEN status = 'completed' AND transaction_type = 'rent' THEN amount ELSE 0 END) as rental_revenue
         FROM transactions 
         WHERE company_id = ? AND DATE(created_at) BETWEEN ? AND ?", 
        [$company_id, $date_from, $date_to]
    );
    
    // أفضل العقارات (الأكثر مشاهدة)
    $top_properties = $database->fetchAll(
        "SELECT title, views_count, price, status 
         FROM properties 
         WHERE company_id = ? 
         ORDER BY views_count DESC 
         LIMIT 5", 
        [$company_id]
    );
    
    // أحدث المعاملات
    $recent_transactions = $database->fetchAll(
        "SELECT t.*, p.title as property_title, c.name as client_name 
         FROM transactions t 
         LEFT JOIN properties p ON t.property_id = p.id 
         LEFT JOIN clients c ON t.client_id = c.id 
         WHERE t.company_id = ? AND DATE(t.created_at) BETWEEN ? AND ?
         ORDER BY t.created_at DESC 
         LIMIT 10", 
        [$company_id, $date_from, $date_to]
    );
    
    // إحصائيات شهرية (آخر 6 أشهر)
    $monthly_stats = $database->fetchAll(
        "SELECT 
            DATE_FORMAT(created_at, '%Y-%m') as month,
            COUNT(*) as transactions_count,
            SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as revenue,
            SUM(CASE WHEN status = 'completed' THEN commission ELSE 0 END) as commission
         FROM transactions 
         WHERE company_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
         GROUP BY DATE_FORMAT(created_at, '%Y-%m')
         ORDER BY month DESC", 
        [$company_id]
    );
    
} catch (Exception $e) {
    $error_message = "حدث خطأ في جلب البيانات";
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../assets/css/style.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        .report-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .report-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        
        .report-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
        }
        
        .stat-item {
            text-align: center;
            padding: 1rem;
            border-radius: 10px;
            background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            margin-bottom: 1rem;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 1rem 0;
        }
        
        .date-filter {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .property-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            border-bottom: 1px solid #e9ecef;
        }
        
        .property-item:last-child {
            border-bottom: none;
        }
        
        .transaction-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            border-bottom: 1px solid #e9ecef;
            font-size: 0.9rem;
        }
        
        .transaction-item:last-child {
            border-bottom: none;
        }
        
        .export-buttons {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2>التقارير والإحصائيات</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="../dashboard.php">لوحة التحكم</a></li>
                                <li class="breadcrumb-item active">التقارير</li>
                            </ol>
                        </nav>
                    </div>
                </div>
                
                <!-- Date Filter -->
                <div class="date-filter">
                    <form method="GET" class="row g-3 align-items-end">
                        <div class="col-md-4">
                            <label for="date_from" class="form-label text-white">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" 
                                   value="<?php echo $date_from; ?>">
                        </div>
                        <div class="col-md-4">
                            <label for="date_to" class="form-label text-white">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" 
                                   value="<?php echo $date_to; ?>">
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-light w-100">
                                <i class="bi bi-funnel me-2"></i>
                                تطبيق الفلتر
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- Export Buttons -->
                <div class="export-buttons">
                    <button class="btn btn-success" onclick="exportToExcel()">
                        <i class="bi bi-file-earmark-excel me-2"></i>
                        تصدير Excel
                    </button>
                    <button class="btn btn-danger" onclick="exportToPDF()">
                        <i class="bi bi-file-earmark-pdf me-2"></i>
                        تصدير PDF
                    </button>
                    <button class="btn btn-info" onclick="window.print()">
                        <i class="bi bi-printer me-2"></i>
                        طباعة
                    </button>
                </div>
                
                <!-- Financial Overview -->
                <div class="report-card">
                    <h5 class="report-title">
                        <i class="bi bi-cash-coin me-2"></i>
                        الملخص المالي (<?php echo formatDate($date_from, 'arabic_date'); ?> - <?php echo formatDate($date_to, 'arabic_date'); ?>)
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stat-item">
                                <div class="stat-number"><?php echo formatCurrency($financial_stats['total_sales'] ?? 0); ?></div>
                                <div class="stat-label">إجمالي المبيعات</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <div class="stat-number"><?php echo formatCurrency($financial_stats['total_commission'] ?? 0); ?></div>
                                <div class="stat-label">إجمالي العمولات</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <div class="stat-number"><?php echo formatCurrency($financial_stats['avg_transaction'] ?? 0); ?></div>
                                <div class="stat-label">متوسط المعاملة</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <div class="stat-number"><?php echo formatNumber($transactions_stats['completed']); ?></div>
                                <div class="stat-label">معاملات مكتملة</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <!-- Properties Statistics -->
                    <div class="col-lg-6">
                        <div class="report-card">
                            <h5 class="report-title">
                                <i class="bi bi-building me-2"></i>
                                إحصائيات العقارات
                            </h5>
                            
                            <div class="chart-container">
                                <canvas id="propertiesChart"></canvas>
                            </div>
                            
                            <div class="row text-center mt-3">
                                <div class="col-3">
                                    <strong class="text-primary"><?php echo $properties_stats['total']; ?></strong>
                                    <br><small>إجمالي</small>
                                </div>
                                <div class="col-3">
                                    <strong class="text-success"><?php echo $properties_stats['available']; ?></strong>
                                    <br><small>متاح</small>
                                </div>
                                <div class="col-3">
                                    <strong class="text-danger"><?php echo $properties_stats['sold']; ?></strong>
                                    <br><small>مباع</small>
                                </div>
                                <div class="col-3">
                                    <strong class="text-warning"><?php echo $properties_stats['rented']; ?></strong>
                                    <br><small>مؤجر</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Clients Statistics -->
                    <div class="col-lg-6">
                        <div class="report-card">
                            <h5 class="report-title">
                                <i class="bi bi-people me-2"></i>
                                إحصائيات العملاء
                            </h5>
                            
                            <div class="chart-container">
                                <canvas id="clientsChart"></canvas>
                            </div>
                            
                            <div class="row text-center mt-3">
                                <div class="col-3">
                                    <strong class="text-success"><?php echo $clients_stats['buyers']; ?></strong>
                                    <br><small>مشترين</small>
                                </div>
                                <div class="col-3">
                                    <strong class="text-primary"><?php echo $clients_stats['sellers']; ?></strong>
                                    <br><small>بائعين</small>
                                </div>
                                <div class="col-3">
                                    <strong class="text-info"><?php echo $clients_stats['renters']; ?></strong>
                                    <br><small>مستأجرين</small>
                                </div>
                                <div class="col-3">
                                    <strong class="text-warning"><?php echo $clients_stats['landlords']; ?></strong>
                                    <br><small>مؤجرين</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <!-- Top Properties -->
                    <div class="col-lg-6">
                        <div class="report-card">
                            <h5 class="report-title">
                                <i class="bi bi-star me-2"></i>
                                العقارات الأكثر مشاهدة
                            </h5>
                            
                            <?php if (empty($top_properties)): ?>
                                <div class="text-center text-muted py-4">
                                    <i class="bi bi-building display-4"></i>
                                    <p class="mt-2">لا توجد بيانات</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($top_properties as $property): ?>
                                    <div class="property-item">
                                        <div>
                                            <strong><?php echo htmlspecialchars($property['title']); ?></strong>
                                            <br>
                                            <small class="text-muted">
                                                <i class="bi bi-eye me-1"></i>
                                                <?php echo formatNumber($property['views_count']); ?> مشاهدة
                                            </small>
                                        </div>
                                        <div class="text-end">
                                            <div class="fw-bold text-primary">
                                                <?php echo formatCurrency($property['price']); ?>
                                            </div>
                                            <span class="badge bg-<?php echo $property['status'] === 'available' ? 'success' : 'secondary'; ?>">
                                                <?php echo PROPERTY_STATUS[$property['status']]; ?>
                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Recent Transactions -->
                    <div class="col-lg-6">
                        <div class="report-card">
                            <h5 class="report-title">
                                <i class="bi bi-clock-history me-2"></i>
                                أحدث المعاملات
                            </h5>
                            
                            <?php if (empty($recent_transactions)): ?>
                                <div class="text-center text-muted py-4">
                                    <i class="bi bi-receipt display-4"></i>
                                    <p class="mt-2">لا توجد معاملات في هذه الفترة</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($recent_transactions as $transaction): ?>
                                    <div class="transaction-item">
                                        <div>
                                            <strong><?php echo htmlspecialchars($transaction['property_title']); ?></strong>
                                            <br>
                                            <small class="text-muted">
                                                <?php echo htmlspecialchars($transaction['client_name']); ?> - 
                                                <?php echo formatDate($transaction['created_at'], 'arabic_date'); ?>
                                            </small>
                                        </div>
                                        <div class="text-end">
                                            <div class="fw-bold text-success">
                                                <?php echo formatCurrency($transaction['amount']); ?>
                                            </div>
                                            <span class="badge bg-<?php echo $transaction['status'] === 'completed' ? 'success' : ($transaction['status'] === 'pending' ? 'warning' : 'danger'); ?>">
                                                <?php 
                                                $status_labels = ['pending' => 'معلقة', 'completed' => 'مكتملة', 'cancelled' => 'ملغية'];
                                                echo $status_labels[$transaction['status']];
                                                ?>
                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Monthly Performance -->
                <?php if (!empty($monthly_stats)): ?>
                    <div class="report-card">
                        <h5 class="report-title">
                            <i class="bi bi-graph-up me-2"></i>
                            الأداء الشهري (آخر 6 أشهر)
                        </h5>
                        
                        <div class="chart-container">
                            <canvas id="monthlyChart"></canvas>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Properties Chart
        const propertiesCtx = document.getElementById('propertiesChart').getContext('2d');
        new Chart(propertiesCtx, {
            type: 'doughnut',
            data: {
                labels: ['متاح', 'مباع', 'مؤجر', 'قيد المراجعة'],
                datasets: [{
                    data: [
                        <?php echo $properties_stats['available']; ?>,
                        <?php echo $properties_stats['sold']; ?>,
                        <?php echo $properties_stats['rented']; ?>,
                        <?php echo $properties_stats['pending']; ?>
                    ],
                    backgroundColor: ['#28a745', '#dc3545', '#ffc107', '#17a2b8']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
        
        // Clients Chart
        const clientsCtx = document.getElementById('clientsChart').getContext('2d');
        new Chart(clientsCtx, {
            type: 'pie',
            data: {
                labels: ['مشترين', 'بائعين', 'مستأجرين', 'مؤجرين'],
                datasets: [{
                    data: [
                        <?php echo $clients_stats['buyers']; ?>,
                        <?php echo $clients_stats['sellers']; ?>,
                        <?php echo $clients_stats['renters']; ?>,
                        <?php echo $clients_stats['landlords']; ?>
                    ],
                    backgroundColor: ['#28a745', '#667eea', '#17a2b8', '#ffc107']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
        
        <?php if (!empty($monthly_stats)): ?>
        // Monthly Performance Chart
        const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
        new Chart(monthlyCtx, {
            type: 'line',
            data: {
                labels: [
                    <?php foreach (array_reverse($monthly_stats) as $stat): ?>
                        '<?php echo date('Y/m', strtotime($stat['month'] . '-01')); ?>',
                    <?php endforeach; ?>
                ],
                datasets: [{
                    label: 'الإيرادات',
                    data: [
                        <?php foreach (array_reverse($monthly_stats) as $stat): ?>
                            <?php echo $stat['revenue']; ?>,
                        <?php endforeach; ?>
                    ],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4
                }, {
                    label: 'العمولات',
                    data: [
                        <?php foreach (array_reverse($monthly_stats) as $stat): ?>
                            <?php echo $stat['commission']; ?>,
                        <?php endforeach; ?>
                    ],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        <?php endif; ?>
        
        // Export functions
        function exportToExcel() {
            alert('ميزة التصدير إلى Excel ستكون متاحة قريباً');
        }
        
        function exportToPDF() {
            alert('ميزة التصدير إلى PDF ستكون متاحة قريباً');
        }
        
        // Animation on load
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.report-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
