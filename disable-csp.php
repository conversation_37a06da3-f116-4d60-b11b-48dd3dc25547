<?php
/**
 * تعطيل Content Security Policy للتطوير
 * Disable CSP for Development
 */

echo "<h1>🔧 إدارة Content Security Policy</h1>";

$action = $_GET['action'] ?? '';

if ($action === 'disable') {
    // إنشاء ملف .htaccess بدون CSP
    $htaccess_content = '# Real Estate Management System .htaccess (No CSP)

# Enable rewrite engine
RewriteEngine On

# Basic security headers only
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Prevent access to sensitive files
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|sql|conf)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Prevent access to config and includes directories
<IfModule mod_rewrite.c>
    RewriteRule ^(config|includes)/ - [F,L]
</IfModule>

# Set default charset
AddDefaultCharset UTF-8

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Prevent directory browsing
Options -Indexes';

    file_put_contents('.htaccess', $htaccess_content);
    echo "<div style='color: green;'>✅ تم تعطيل CSP بنجاح!</div>";
    echo "<p>تم إزالة قيود Content Security Policy. يمكنك الآن استخدام النظام بدون مشاكل الخطوط.</p>";
    
} elseif ($action === 'enable') {
    // استعادة ملف .htaccess مع CSP
    if (file_exists('.htaccess.backup')) {
        copy('.htaccess.backup', '.htaccess');
        echo "<div style='color: green;'>✅ تم تفعيل CSP بنجاح!</div>";
        echo "<p>تم استعادة إعدادات الأمان الكاملة.</p>";
    } else {
        echo "<div style='color: red;'>❌ لم يتم العثور على ملف النسخة الاحتياطية</div>";
    }
    
} elseif ($action === 'fix') {
    // إصلاح CSP لتشمل جميع المصادر المطلوبة
    $htaccess_content = '# Real Estate Management System .htaccess (Fixed CSP)

# Enable rewrite engine
RewriteEngine On

# Security headers with proper CSP
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Content-Security-Policy "default-src \'self\'; script-src \'self\' \'unsafe-inline\' \'unsafe-eval\' https://cdn.jsdelivr.net https://www.google.com https://maps.googleapis.com; style-src \'self\' \'unsafe-inline\' https://cdn.jsdelivr.net https://fonts.googleapis.com; font-src \'self\' https://fonts.gstatic.com https://cdn.jsdelivr.net data:; img-src \'self\' data: blob: https: http:; frame-src \'self\' https://www.google.com https://maps.google.com; connect-src \'self\' https:;"
</IfModule>

# Prevent access to sensitive files
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|sql|conf)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Prevent access to config and includes directories
<IfModule mod_rewrite.c>
    RewriteRule ^(config|includes)/ - [F,L]
</IfModule>

# Set default charset
AddDefaultCharset UTF-8

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Prevent directory browsing
Options -Indexes';

    file_put_contents('.htaccess', $htaccess_content);
    echo "<div style='color: green;'>✅ تم إصلاح CSP بنجاح!</div>";
    echo "<p>تم تحديث إعدادات CSP لتشمل جميع المصادر المطلوبة مع الحفاظ على الأمان.</p>";
    
} else {
    // عرض الخيارات
    echo "<h2>اختر الإجراء المطلوب:</h2>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='?action=disable' style='background: #dc3545; color: white; padding: 15px 25px; text-decoration: none; border-radius: 5px; margin: 10px; display: inline-block;'>";
    echo "🚫 تعطيل CSP (للتطوير)";
    echo "</a>";
    echo "<br><small style='color: #666;'>يزيل جميع قيود CSP - أقل أماناً لكن بدون مشاكل</small>";
    echo "</div>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='?action=fix' style='background: #ffc107; color: black; padding: 15px 25px; text-decoration: none; border-radius: 5px; margin: 10px; display: inline-block;'>";
    echo "🔧 إصلاح CSP (موصى به)";
    echo "</a>";
    echo "<br><small style='color: #666;'>يحدث CSP ليشمل جميع المصادر المطلوبة مع الحفاظ على الأمان</small>";
    echo "</div>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='?action=enable' style='background: #28a745; color: white; padding: 15px 25px; text-decoration: none; border-radius: 5px; margin: 10px; display: inline-block;'>";
    echo "✅ تفعيل CSP الكامل";
    echo "</a>";
    echo "<br><small style='color: #666;'>يستعيد إعدادات الأمان الكاملة</small>";
    echo "</div>";
    
    echo "<h3>الحالة الحالية:</h3>";
    if (file_exists('.htaccess')) {
        $htaccess_content = file_get_contents('.htaccess');
        if (strpos($htaccess_content, 'Content-Security-Policy') !== false) {
            echo "<div style='color: orange;'>🟡 CSP مفعل</div>";
        } else {
            echo "<div style='color: red;'>🔴 CSP معطل</div>";
        }
    } else {
        echo "<div style='color: gray;'>⚪ لا يوجد ملف .htaccess</div>";
    }
}

echo "<br><div style='margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;'>";
echo "<a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>العودة للنظام</a>";
echo "<a href='test-connection.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار الاتصال</a>";
echo "</div>";

echo "<div style='margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;'>";
echo "<h4>ما هو Content Security Policy (CSP)؟</h4>";
echo "<p>CSP هو آلية أمان تساعد في منع هجمات XSS عبر تحديد المصادر المسموح بتحميلها في الموقع.</p>";
echo "<p><strong>للتطوير:</strong> يُنصح بتعطيل أو تخفيف CSP لتجنب مشاكل التوافق.</p>";
echo "<p><strong>للإنتاج:</strong> يُنصح بتفعيل CSP الكامل لحماية أفضل.</p>";
echo "</div>";
?>
