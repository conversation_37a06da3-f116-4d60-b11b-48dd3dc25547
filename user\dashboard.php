<?php
/**
 * لوحة تحكم المستخدم
 * User Dashboard
 */

require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../login.php');
}

$user = getCurrentUser();
$company_id = $user['company_id'];

// جلب الإحصائيات
try {
    // إحصائيات العقارات
    $total_properties = $database->count('properties', 'company_id = ?', [$company_id]);
    $available_properties = $database->count('properties', 'company_id = ? AND status = ?', [$company_id, 'available']);
    $sold_properties = $database->count('properties', 'company_id = ? AND status = ?', [$company_id, 'sold']);
    $rented_properties = $database->count('properties', 'company_id = ? AND status = ?', [$company_id, 'rented']);
    
    // إحصائيات العملاء
    $total_clients = $database->count('clients', 'company_id = ?', [$company_id]);
    
    // إحصائيات المعاملات
    $total_transactions = $database->count('transactions', 'company_id = ?', [$company_id]);
    $completed_transactions = $database->count('transactions', 'company_id = ? AND status = ?', [$company_id, 'completed']);
    
    // إجمالي العمولات
    $commission_result = $database->fetch(
        "SELECT SUM(commission) as total_commission FROM transactions WHERE company_id = ? AND status = 'completed'", 
        [$company_id]
    );
    $total_commission = $commission_result['total_commission'] ?? 0;
    
    // أحدث العقارات
    $recent_properties = $database->fetchAll(
        "SELECT * FROM properties WHERE company_id = ? ORDER BY created_at DESC LIMIT 5", 
        [$company_id]
    );
    
    // أحدث المعاملات
    $recent_transactions = $database->fetchAll(
        "SELECT t.*, p.title as property_title, c.name as client_name 
         FROM transactions t 
         LEFT JOIN properties p ON t.property_id = p.id 
         LEFT JOIN clients c ON t.client_id = c.id 
         WHERE t.company_id = ? 
         ORDER BY t.created_at DESC LIMIT 5", 
        [$company_id]
    );
    
} catch (Exception $e) {
    $error_message = "حدث خطأ في جلب البيانات";
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 15px;
        }
        
        .welcome-message {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .company-info {
            opacity: 0.9;
            font-size: 1rem;
        }
        
        .quick-actions {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        
        .quick-action-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            padding: 1.5rem;
            border-radius: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            height: 120px;
        }
        
        .quick-action-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            color: white;
        }
        
        .quick-action-btn i {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .recent-section {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        
        .section-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
        }
        
        .property-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .property-item:hover {
            background-color: #f8f9fa;
        }
        
        .property-image {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            object-fit: cover;
            margin-left: 1rem;
        }
        
        .property-info h6 {
            margin: 0;
            font-weight: 600;
            color: #333;
        }
        
        .property-info small {
            color: #666;
        }
        
        .property-price {
            font-weight: 700;
            color: #667eea;
        }
        
        .transaction-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #e9ecef;
        }
        
        .transaction-info h6 {
            margin: 0;
            font-weight: 600;
        }
        
        .transaction-amount {
            font-weight: 700;
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- Header -->
                <div class="dashboard-header">
                    <div class="container-fluid">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="welcome-message">
                                    مرحباً، <?php echo htmlspecialchars($user['full_name']); ?>
                                </div>
                                <div class="company-info">
                                    <i class="bi bi-building me-2"></i>
                                    <?php echo htmlspecialchars($user['company_name']); ?>
                                </div>
                            </div>
                            <div class="col-md-4 text-md-end">
                                <div class="text-white-50">
                                    <i class="bi bi-calendar me-2"></i>
                                    <?php echo formatDate(date('Y-m-d H:i:s'), 'arabic_datetime'); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stats-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="stats-number"><?php echo formatNumber($total_properties); ?></div>
                                    <div class="stats-label">إجمالي العقارات</div>
                                </div>
                                <div class="stats-icon">
                                    <i class="bi bi-building"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stats-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="stats-number"><?php echo formatNumber($total_clients); ?></div>
                                    <div class="stats-label">إجمالي العملاء</div>
                                </div>
                                <div class="stats-icon">
                                    <i class="bi bi-people"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stats-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="stats-number"><?php echo formatNumber($completed_transactions); ?></div>
                                    <div class="stats-label">المعاملات المكتملة</div>
                                </div>
                                <div class="stats-icon">
                                    <i class="bi bi-check-circle"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stats-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="stats-number"><?php echo formatCurrency($total_commission); ?></div>
                                    <div class="stats-label">إجمالي العمولات</div>
                                </div>
                                <div class="stats-icon">
                                    <i class="bi bi-cash-coin"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="quick-actions">
                    <h5 class="section-title">الإجراءات السريعة</h5>
                    <div class="row">
                        <div class="col-lg-2 col-md-4 col-6 mb-3">
                            <a href="properties/add.php" class="quick-action-btn">
                                <i class="bi bi-plus-circle"></i>
                                <span>إضافة عقار</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6 mb-3">
                            <a href="clients/add.php" class="quick-action-btn">
                                <i class="bi bi-person-plus"></i>
                                <span>إضافة عميل</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6 mb-3">
                            <a href="transactions/add.php" class="quick-action-btn">
                                <i class="bi bi-receipt"></i>
                                <span>معاملة جديدة</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6 mb-3">
                            <a href="properties/" class="quick-action-btn">
                                <i class="bi bi-search"></i>
                                <span>البحث</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6 mb-3">
                            <a href="reports/" class="quick-action-btn">
                                <i class="bi bi-graph-up"></i>
                                <span>التقارير</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6 mb-3">
                            <a href="settings/" class="quick-action-btn">
                                <i class="bi bi-gear"></i>
                                <span>الإعدادات</span>
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <!-- Recent Properties -->
                    <div class="col-lg-6">
                        <div class="recent-section">
                            <h5 class="section-title">أحدث العقارات</h5>
                            <?php if (empty($recent_properties)): ?>
                                <div class="text-center text-muted py-4">
                                    <i class="bi bi-building display-4"></i>
                                    <p class="mt-2">لا توجد عقارات بعد</p>
                                    <a href="properties/add.php" class="btn btn-primary">إضافة عقار جديد</a>
                                </div>
                            <?php else: ?>
                                <?php foreach ($recent_properties as $property): ?>
                                    <div class="property-item">
                                        <img src="<?php echo generateImagePlaceholder(60, 60, $property['id']); ?>" 
                                             alt="<?php echo htmlspecialchars($property['title']); ?>" 
                                             class="property-image">
                                        <div class="property-info flex-grow-1">
                                            <h6><?php echo htmlspecialchars($property['title']); ?></h6>
                                            <small class="text-muted">
                                                <i class="bi bi-geo-alt me-1"></i>
                                                <?php echo htmlspecialchars($property['city']); ?>
                                                •
                                                <i class="bi bi-calendar me-1"></i>
                                                <?php echo formatDate($property['created_at'], 'arabic_date'); ?>
                                            </small>
                                        </div>
                                        <div class="property-price">
                                            <?php echo formatCurrency($property['price']); ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                                <div class="text-center mt-3">
                                    <a href="properties/" class="btn btn-outline-primary">عرض جميع العقارات</a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Recent Transactions -->
                    <div class="col-lg-6">
                        <div class="recent-section">
                            <h5 class="section-title">أحدث المعاملات</h5>
                            <?php if (empty($recent_transactions)): ?>
                                <div class="text-center text-muted py-4">
                                    <i class="bi bi-receipt display-4"></i>
                                    <p class="mt-2">لا توجد معاملات بعد</p>
                                    <a href="transactions/add.php" class="btn btn-primary">إضافة معاملة جديدة</a>
                                </div>
                            <?php else: ?>
                                <?php foreach ($recent_transactions as $transaction): ?>
                                    <div class="transaction-item">
                                        <div class="transaction-info">
                                            <h6><?php echo htmlspecialchars($transaction['property_title']); ?></h6>
                                            <small class="text-muted">
                                                <i class="bi bi-person me-1"></i>
                                                <?php echo htmlspecialchars($transaction['client_name']); ?>
                                                •
                                                <i class="bi bi-calendar me-1"></i>
                                                <?php echo formatDate($transaction['created_at'], 'arabic_date'); ?>
                                            </small>
                                        </div>
                                        <div class="transaction-amount">
                                            <?php echo formatCurrency($transaction['amount']); ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                                <div class="text-center mt-3">
                                    <a href="transactions/" class="btn btn-outline-primary">عرض جميع المعاملات</a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تحديث الوقت كل دقيقة
        setInterval(function() {
            const now = new Date();
            const options = { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric', 
                hour: '2-digit', 
                minute: '2-digit' 
            };
            const timeString = now.toLocaleDateString('ar-SA', options);
            document.querySelector('.text-white-50').innerHTML = 
                '<i class="bi bi-calendar me-2"></i>' + timeString;
        }, 60000);
        
        // تأثيرات الحركة للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stats-card, .recent-section, .quick-actions');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
