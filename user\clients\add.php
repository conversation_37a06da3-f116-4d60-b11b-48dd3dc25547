<?php
/**
 * صفحة إضافة عميل جديد
 * Add New Client Page
 */

require_once '../../config/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../../login.php');
}

$user = getCurrentUser();
$company_id = $user['company_id'];

$error_message = '';
$success_message = '';

// معالجة إضافة العميل
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = [
        'name' => sanitize($_POST['name'] ?? ''),
        'email' => sanitize($_POST['email'] ?? ''),
        'phone' => sanitize($_POST['phone'] ?? ''),
        'national_id' => sanitize($_POST['national_id'] ?? ''),
        'address' => sanitize($_POST['address'] ?? ''),
        'client_type' => sanitize($_POST['client_type'] ?? ''),
        'notes' => sanitize($_POST['notes'] ?? '')
    ];
    
    // التحقق من البيانات المطلوبة
    if (empty($data['name']) || empty($data['phone']) || empty($data['client_type'])) {
        $error_message = 'الاسم ورقم الهاتف ونوع العميل مطلوبة';
    } elseif (!empty($data['email']) && !isValidEmail($data['email'])) {
        $error_message = 'البريد الإلكتروني غير صحيح';
    } elseif (!isValidSaudiPhone($data['phone'])) {
        $error_message = 'رقم الهاتف غير صحيح';
    } elseif (!array_key_exists($data['client_type'], CLIENT_TYPES)) {
        $error_message = 'نوع العميل غير صحيح';
    } else {
        try {
            // التحقق من عدم تكرار البيانات
            $existing_client = null;
            
            if (!empty($data['email'])) {
                $existing_client = $database->fetch(
                    "SELECT id FROM clients WHERE company_id = ? AND email = ?", 
                    [$company_id, $data['email']]
                );
            }
            
            if (!$existing_client) {
                $existing_client = $database->fetch(
                    "SELECT id FROM clients WHERE company_id = ? AND phone = ?", 
                    [$company_id, $data['phone']]
                );
            }
            
            if (!$existing_client && !empty($data['national_id'])) {
                $existing_client = $database->fetch(
                    "SELECT id FROM clients WHERE company_id = ? AND national_id = ?", 
                    [$company_id, $data['national_id']]
                );
            }
            
            if ($existing_client) {
                $error_message = 'عميل بنفس البيانات موجود مسبقاً';
            } else {
                // إضافة بيانات الشركة والمستخدم
                $data['company_id'] = $company_id;
                $data['user_id'] = $user['id'];
                
                // تنسيق رقم الهاتف
                $data['phone'] = formatPhone($data['phone']);
                
                // إدراج العميل
                $client_id = $database->insert('clients', $data);
                
                setAlert('تم إضافة العميل بنجاح', 'success');
                redirect('view.php?id=' . $client_id);
            }
            
        } catch (Exception $e) {
            $error_message = 'حدث خطأ أثناء إضافة العميل: ' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة عميل جديد - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <style>
        .form-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .section-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
        }
        
        .required {
            color: #dc3545;
        }
        
        .client-type-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .client-type-card {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .client-type-card:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }
        
        .client-type-card.selected {
            border-color: #667eea;
            background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
        }
        
        .client-type-card input[type="radio"] {
            position: absolute;
            opacity: 0;
            pointer-events: none;
        }
        
        .client-type-icon {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .client-type-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .client-type-description {
            font-size: 0.9rem;
            color: #666;
        }
        
        .phone-input-group {
            position: relative;
        }
        
        .phone-prefix {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            font-weight: 500;
            z-index: 3;
        }
        
        .phone-input {
            padding-left: 4rem;
        }
        
        .form-floating .phone-input {
            padding-left: 4rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2>إضافة عميل جديد</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="../dashboard.php">لوحة التحكم</a></li>
                                <li class="breadcrumb-item"><a href="index.php">العملاء</a></li>
                                <li class="breadcrumb-item active">إضافة عميل</li>
                            </ol>
                        </nav>
                    </div>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-right me-2"></i>
                        العودة للقائمة
                    </a>
                </div>
                
                <!-- Alerts -->
                <?php if ($error_message): ?>
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>
                
                <?php showAlert(); ?>
                
                <!-- Form -->
                <form method="POST" id="clientForm">
                    <!-- Client Type Selection -->
                    <div class="form-section">
                        <h5 class="section-title">
                            <i class="bi bi-person-badge me-2"></i>
                            نوع العميل <span class="required">*</span>
                        </h5>
                        
                        <div class="client-type-cards">
                            <div class="client-type-card" onclick="selectClientType('buyer', this)">
                                <input type="radio" name="client_type" value="buyer" id="buyer" 
                                       <?php echo ($data['client_type'] ?? '') === 'buyer' ? 'checked' : ''; ?>>
                                <div class="client-type-icon">
                                    <i class="bi bi-cart-check"></i>
                                </div>
                                <div class="client-type-title">مشتري</div>
                                <div class="client-type-description">عميل يبحث عن شراء عقار</div>
                            </div>
                            
                            <div class="client-type-card" onclick="selectClientType('seller', this)">
                                <input type="radio" name="client_type" value="seller" id="seller"
                                       <?php echo ($data['client_type'] ?? '') === 'seller' ? 'checked' : ''; ?>>
                                <div class="client-type-icon">
                                    <i class="bi bi-house-door"></i>
                                </div>
                                <div class="client-type-title">بائع</div>
                                <div class="client-type-description">عميل يريد بيع عقاره</div>
                            </div>
                            
                            <div class="client-type-card" onclick="selectClientType('renter', this)">
                                <input type="radio" name="client_type" value="renter" id="renter"
                                       <?php echo ($data['client_type'] ?? '') === 'renter' ? 'checked' : ''; ?>>
                                <div class="client-type-icon">
                                    <i class="bi bi-key"></i>
                                </div>
                                <div class="client-type-title">مستأجر</div>
                                <div class="client-type-description">عميل يبحث عن استئجار عقار</div>
                            </div>
                            
                            <div class="client-type-card" onclick="selectClientType('landlord', this)">
                                <input type="radio" name="client_type" value="landlord" id="landlord"
                                       <?php echo ($data['client_type'] ?? '') === 'landlord' ? 'checked' : ''; ?>>
                                <div class="client-type-icon">
                                    <i class="bi bi-building"></i>
                                </div>
                                <div class="client-type-title">مؤجر</div>
                                <div class="client-type-description">عميل يريد تأجير عقاره</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Basic Information -->
                    <div class="form-section">
                        <h5 class="section-title">
                            <i class="bi bi-person me-2"></i>
                            المعلومات الأساسية
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="name" name="name" 
                                           placeholder="الاسم الكامل" required
                                           value="<?php echo htmlspecialchars($data['name'] ?? ''); ?>">
                                    <label for="name">
                                        الاسم الكامل <span class="required">*</span>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <div class="phone-input-group">
                                        <input type="tel" class="form-control phone-input" id="phone" name="phone" 
                                               placeholder="رقم الهاتف" required
                                               value="<?php echo htmlspecialchars($data['phone'] ?? ''); ?>">
                                        <span class="phone-prefix">+966</span>
                                    </div>
                                    <label for="phone">
                                        رقم الهاتف <span class="required">*</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="email" class="form-control" id="email" name="email" 
                                           placeholder="البريد الإلكتروني"
                                           value="<?php echo htmlspecialchars($data['email'] ?? ''); ?>">
                                    <label for="email">البريد الإلكتروني (اختياري)</label>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="national_id" name="national_id" 
                                           placeholder="رقم الهوية الوطنية"
                                           value="<?php echo htmlspecialchars($data['national_id'] ?? ''); ?>">
                                    <label for="national_id">رقم الهوية الوطنية (اختياري)</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <textarea class="form-control" id="address" name="address" 
                                      placeholder="العنوان" style="height: 100px"><?php echo htmlspecialchars($data['address'] ?? ''); ?></textarea>
                            <label for="address">العنوان (اختياري)</label>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <textarea class="form-control" id="notes" name="notes" 
                                      placeholder="ملاحظات" style="height: 120px"><?php echo htmlspecialchars($data['notes'] ?? ''); ?></textarea>
                            <label for="notes">ملاحظات إضافية (اختياري)</label>
                        </div>
                    </div>
                    
                    <!-- Submit Buttons -->
                    <div class="form-section">
                        <div class="d-flex gap-3">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-check-circle me-2"></i>
                                حفظ العميل
                            </button>
                            <a href="index.php" class="btn btn-outline-secondary btn-lg">
                                <i class="bi bi-x-circle me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // اختيار نوع العميل
        function selectClientType(type, card) {
            // إزالة التحديد من جميع البطاقات
            document.querySelectorAll('.client-type-card').forEach(c => {
                c.classList.remove('selected');
            });
            
            // إضافة التحديد للبطاقة المختارة
            card.classList.add('selected');
            
            // تحديد الراديو بوتن
            document.getElementById(type).checked = true;
        }
        
        // تحديد البطاقة المختارة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            const selectedType = document.querySelector('input[name="client_type"]:checked');
            if (selectedType) {
                const card = selectedType.closest('.client-type-card');
                if (card) {
                    card.classList.add('selected');
                }
            }
        });
        
        // تنسيق رقم الهاتف
        document.getElementById('phone').addEventListener('input', function() {
            let phone = this.value.replace(/\D/g, '');
            
            // إزالة +966 إذا كانت موجودة
            if (phone.startsWith('966')) {
                phone = phone.substring(3);
            }
            
            // إزالة 0 من البداية إذا كانت موجودة
            if (phone.startsWith('0')) {
                phone = phone.substring(1);
            }
            
            // التأكد من أن الرقم يبدأ بـ 5
            if (phone.length > 0 && !phone.startsWith('5')) {
                phone = '5' + phone;
            }
            
            // تحديد الطول الأقصى
            if (phone.length > 9) {
                phone = phone.substring(0, 9);
            }
            
            this.value = phone;
        });
        
        // التحقق من صحة رقم الهوية
        document.getElementById('national_id').addEventListener('input', function() {
            let id = this.value.replace(/\D/g, '');
            
            if (id.length > 10) {
                id = id.substring(0, 10);
            }
            
            this.value = id;
        });
        
        // التحقق من صحة النموذج
        document.getElementById('clientForm').addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const phone = document.getElementById('phone').value.trim();
            const clientType = document.querySelector('input[name="client_type"]:checked');
            const email = document.getElementById('email').value.trim();
            
            if (!name) {
                e.preventDefault();
                alert('يرجى إدخال اسم العميل');
                document.getElementById('name').focus();
                return false;
            }
            
            if (!phone) {
                e.preventDefault();
                alert('يرجى إدخال رقم الهاتف');
                document.getElementById('phone').focus();
                return false;
            }
            
            if (phone.length !== 9 || !phone.startsWith('5')) {
                e.preventDefault();
                alert('رقم الهاتف غير صحيح. يجب أن يبدأ بـ 5 ويكون 9 أرقام');
                document.getElementById('phone').focus();
                return false;
            }
            
            if (!clientType) {
                e.preventDefault();
                alert('يرجى اختيار نوع العميل');
                return false;
            }
            
            if (email && !isValidEmail(email)) {
                e.preventDefault();
                alert('البريد الإلكتروني غير صحيح');
                document.getElementById('email').focus();
                return false;
            }
            
            // إظهار مؤشر التحميل
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري الحفظ...';
        });
        
        // التحقق من صحة البريد الإلكتروني
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('.form-section');
            sections.forEach((section, index) => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    section.style.transition = 'all 0.5s ease';
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
