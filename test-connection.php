<?php
/**
 * اختبار الاتصال بقاعدة البيانات
 * Database Connection Test
 */

echo "<h2>اختبار الاتصال بقاعدة البيانات</h2>";

try {
    require_once 'config/database.php';
    
    echo "<div style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح!</div>";
    
    // اختبار الجداول
    $tables = ['companies', 'users', 'properties', 'clients', 'transactions', 'property_images', 'settings'];
    
    echo "<h3>فحص الجداول:</h3>";
    foreach ($tables as $table) {
        try {
            $stmt = $db->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<div style='color: green;'>✅ جدول $table: $count سجل</div>";
        } catch (Exception $e) {
            echo "<div style='color: red;'>❌ خطأ في جدول $table: " . $e->getMessage() . "</div>";
        }
    }
    
    // اختبار البيانات التجريبية
    echo "<h3>البيانات التجريبية:</h3>";
    $users = $database->fetchAll("SELECT username, email, role FROM users");
    foreach ($users as $user) {
        echo "<div>👤 {$user['username']} ({$user['email']}) - {$user['role']}</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ خطأ في الاتصال: " . $e->getMessage() . "</div>";
    echo "<div>تأكد من:</div>";
    echo "<ul>";
    echo "<li>تشغيل خدمة MySQL</li>";
    echo "<li>صحة بيانات الاتصال في config/database.php</li>";
    echo "<li>وجود قاعدة البيانات</li>";
    echo "</ul>";
}

echo "<br><a href='index.php'>العودة للصفحة الرئيسية</a>";
?>
