<?php
/**
 * تحديث تواريخ انتهاء الاشتراك
 * Update Subscription Expiry Dates
 */

require_once 'config/config.php';

try {
    // تحديث تواريخ انتهاء الاشتراك لجميع الشركات
    $sql = "UPDATE companies SET 
            subscription_expires = CASE 
                WHEN subscription_plan = 'premium' THEN '2025-12-31'
                WHEN subscription_plan = 'basic' THEN '2025-06-30'
                WHEN subscription_plan = 'enterprise' THEN '2025-12-31'
                ELSE DATE_ADD(CURDATE(), INTERVAL 1 YEAR)
            END,
            status = 'active'
            WHERE status != 'active' OR subscription_expires < CURDATE()";
    
    $result = $database->query($sql);
    
    echo "<h2>✅ تم تحديث تواريخ الاشتراك بنجاح!</h2>";
    
    // عرض الشركات المحدثة
    $companies = $database->fetchAll("SELECT name, subscription_plan, subscription_expires, status FROM companies");
    
    echo "<h3>الشركات المحدثة:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th style='padding: 10px;'>اسم الشركة</th>";
    echo "<th style='padding: 10px;'>خطة الاشتراك</th>";
    echo "<th style='padding: 10px;'>تاريخ الانتهاء</th>";
    echo "<th style='padding: 10px;'>الحالة</th>";
    echo "</tr>";
    
    foreach ($companies as $company) {
        echo "<tr>";
        echo "<td style='padding: 10px;'>" . htmlspecialchars($company['name']) . "</td>";
        echo "<td style='padding: 10px;'>" . htmlspecialchars($company['subscription_plan']) . "</td>";
        echo "<td style='padding: 10px;'>" . htmlspecialchars($company['subscription_expires']) . "</td>";
        echo "<td style='padding: 10px; color: " . ($company['status'] === 'active' ? 'green' : 'red') . ";'>" . htmlspecialchars($company['status']) . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<br><p><strong>ملاحظة:</strong> تم تحديث جميع الاشتراكات لتكون صالحة حتى نهاية عام 2025.</p>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ خطأ في التحديث:</h2>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<br><a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>العودة للنظام</a>";
echo " ";
echo "<a href='test-connection.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار الاتصال</a>";
?>
