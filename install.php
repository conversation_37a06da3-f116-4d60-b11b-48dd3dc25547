<?php
/**
 * ملف التثبيت
 * Installation File
 */

$step = $_GET['step'] ?? 1;
$error = '';
$success = '';

// معالجة خطوات التثبيت
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step == 1) {
        // التحقق من متطلبات النظام
        $requirements_met = true;
        
        if (version_compare(PHP_VERSION, '7.4.0', '<')) {
            $requirements_met = false;
            $error = 'يتطلب النظام PHP 7.4 أو أحدث';
        }
        
        if (!extension_loaded('pdo') || !extension_loaded('pdo_mysql')) {
            $requirements_met = false;
            $error = 'يتطلب النظام PDO و PDO MySQL';
        }
        
        if (!extension_loaded('gd')) {
            $requirements_met = false;
            $error = 'يتطلب النظام GD Library لمعالجة الصور';
        }
        
        if ($requirements_met) {
            header('Location: install.php?step=2');
            exit;
        }
    } elseif ($step == 2) {
        // إعداد قاعدة البيانات
        $host = $_POST['host'] ?? 'localhost';
        $dbname = $_POST['dbname'] ?? 'real_estate_saas';
        $username = $_POST['username'] ?? 'root';
        $password = $_POST['password'] ?? '';
        $reset_db = isset($_POST['reset_db']);
        
        try {
            // الاتصال بقاعدة البيانات
            $dsn = "mysql:host=$host;charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // إنشاء قاعدة البيانات إذا لم تكن موجودة
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            $pdo->exec("USE `$dbname`");
            
            // التحقق من وجود الجداول
            $tables_exist = false;
            try {
                $result = $pdo->query("SHOW TABLES LIKE 'companies'");
                $tables_exist = $result->rowCount() > 0;
            } catch (Exception $e) {
                // الجداول غير موجودة
            }

            if (!$tables_exist || $reset_db) {
                if ($reset_db && $tables_exist) {
                    // حذف الجداول الموجودة
                    $tables_to_drop = ['transactions', 'property_images', 'properties', 'clients', 'users', 'settings', 'companies'];
                    foreach ($tables_to_drop as $table) {
                        try {
                            $pdo->exec("DROP TABLE IF EXISTS $table");
                        } catch (Exception $e) {
                            // تجاهل أخطاء الحذف
                        }
                    }
                }

                // قراءة وتنفيذ ملف SQL
                $sql = file_get_contents('database.sql');
                $statements = explode(';', $sql);

                foreach ($statements as $statement) {
                    $statement = trim($statement);
                    if (!empty($statement) && !stripos($statement, 'CREATE DATABASE')) {
                        try {
                            $pdo->exec($statement);
                        } catch (Exception $e) {
                            // تجاهل أخطاء الجداول الموجودة
                            if (!stripos($e->getMessage(), 'already exists')) {
                                throw $e;
                            }
                        }
                    }
                }
            }
            
            // تحديث ملف الإعدادات
            $config_content = file_get_contents('config/database.php');
            $config_content = str_replace("define('DB_HOST', 'localhost');", "define('DB_HOST', '$host');", $config_content);
            $config_content = str_replace("define('DB_NAME', 'real_estate_saas');", "define('DB_NAME', '$dbname');", $config_content);
            $config_content = str_replace("define('DB_USER', 'root');", "define('DB_USER', '$username');", $config_content);
            $config_content = str_replace("define('DB_PASS', '');", "define('DB_PASS', '$password');", $config_content);
            
            file_put_contents('config/database.php', $config_content);
            
            if ($tables_exist && !$reset_db) {
                $success = 'قاعدة البيانات موجودة مسبقاً. تم تحديث الإعدادات فقط.';
            } else {
                $success = 'تم إعداد قاعدة البيانات بنجاح!';
            }
            header('Location: install.php?step=3');
            exit;
            
        } catch (Exception $e) {
            $error = 'خطأ في إعداد قاعدة البيانات: ' . $e->getMessage();
        }
    } elseif ($step == 3) {
        // إنشاء ملف تأكيد التثبيت
        file_put_contents('.installed', date('Y-m-d H:i:s'));
        header('Location: index.php');
        exit;
    }
}

// التحقق من التثبيت المسبق
if (file_exists('.installed') && $step == 1) {
    header('Location: index.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام إدارة العقارات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .install-container {
            max-width: 600px;
            margin: 0 auto;
        }
        .install-card {
            background: white;
            border-radius: 20px;
            padding: 2.5rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 0.5rem;
            font-weight: bold;
        }
        .step.active {
            background: #667eea;
            color: white;
        }
        .step.completed {
            background: #28a745;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="install-container">
            <div class="install-card">
                <div class="text-center mb-4">
                    <h2>تثبيت نظام إدارة العقارات</h2>
                    <p class="text-muted">مرحباً بك في معالج التثبيت</p>
                </div>
                
                <!-- Step Indicator -->
                <div class="step-indicator">
                    <div class="step <?php echo $step >= 1 ? ($step == 1 ? 'active' : 'completed') : ''; ?>">1</div>
                    <div class="step <?php echo $step >= 2 ? ($step == 2 ? 'active' : 'completed') : ''; ?>">2</div>
                    <div class="step <?php echo $step >= 3 ? ($step == 3 ? 'active' : 'completed') : ''; ?>">3</div>
                </div>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
                <?php endif; ?>
                
                <?php if ($step == 1): ?>
                    <!-- Step 1: Requirements Check -->
                    <h4>الخطوة 1: فحص المتطلبات</h4>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center p-2 border-bottom">
                            <span>إصدار PHP (7.4+)</span>
                            <span class="badge bg-<?php echo version_compare(PHP_VERSION, '7.4.0', '>=') ? 'success' : 'danger'; ?>">
                                <?php echo PHP_VERSION; ?>
                            </span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center p-2 border-bottom">
                            <span>PDO MySQL</span>
                            <span class="badge bg-<?php echo extension_loaded('pdo_mysql') ? 'success' : 'danger'; ?>">
                                <?php echo extension_loaded('pdo_mysql') ? 'متوفر' : 'غير متوفر'; ?>
                            </span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center p-2 border-bottom">
                            <span>GD Library</span>
                            <span class="badge bg-<?php echo extension_loaded('gd') ? 'success' : 'danger'; ?>">
                                <?php echo extension_loaded('gd') ? 'متوفر' : 'غير متوفر'; ?>
                            </span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center p-2">
                            <span>مجلد uploads قابل للكتابة</span>
                            <span class="badge bg-<?php echo is_writable('uploads') || mkdir('uploads', 0755, true) ? 'success' : 'danger'; ?>">
                                <?php echo is_writable('uploads') ? 'متوفر' : 'غير متوفر'; ?>
                            </span>
                        </div>
                    </div>
                    
                    <form method="POST">
                        <button type="submit" class="btn btn-primary w-100">التالي</button>
                    </form>
                    
                <?php elseif ($step == 2): ?>
                    <!-- Step 2: Database Setup -->
                    <h4>الخطوة 2: إعداد قاعدة البيانات</h4>

                    <div class="alert alert-info">
                        <strong>ملاحظة:</strong> إذا كانت قاعدة البيانات موجودة مسبقاً، سيتم تحديث الإعدادات فقط.
                        لإعادة إنشاء قاعدة البيانات من جديد، فعّل خيار "إعادة تعيين قاعدة البيانات".
                    </div>

                    <form method="POST">
                        <div class="mb-3">
                            <label for="host" class="form-label">خادم قاعدة البيانات</label>
                            <input type="text" class="form-control" id="host" name="host" value="localhost" required>
                        </div>
                        <div class="mb-3">
                            <label for="dbname" class="form-label">اسم قاعدة البيانات</label>
                            <input type="text" class="form-control" id="dbname" name="dbname" value="real_estate_saas" required>
                        </div>
                        <div class="mb-3">
                            <label for="username" class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="username" name="username" value="root" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" id="password" name="password">
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="reset_db" name="reset_db" onchange="toggleWarning()">
                            <label class="form-check-label" for="reset_db">
                                <span class="text-danger">⚠️ إعادة تعيين قاعدة البيانات (حذف جميع البيانات الموجودة)</span>
                            </label>
                        </div>
                        <div id="resetWarning" class="alert alert-danger" style="display: none;">
                            <strong>تحذير:</strong> هذا الخيار سيحذف جميع البيانات الموجودة في قاعدة البيانات نهائياً!
                        </div>
                        <button type="submit" class="btn btn-primary w-100">إنشاء قاعدة البيانات</button>
                    </form>
                    
                <?php elseif ($step == 3): ?>
                    <!-- Step 3: Completion -->
                    <h4>الخطوة 3: إكمال التثبيت</h4>
                    <div class="text-center">
                        <i class="bi bi-check-circle text-success" style="font-size: 4rem;"></i>
                        <h5 class="mt-3">تم التثبيت بنجاح!</h5>
                        <p class="text-muted">يمكنك الآن استخدام النظام</p>
                        
                        <div class="alert alert-info text-start">
                            <strong>بيانات تسجيل الدخول التجريبية:</strong><br>
                            البريد: <EMAIL><br>
                            كلمة المرور: password
                        </div>
                        
                        <form method="POST">
                            <button type="submit" class="btn btn-success btn-lg">الانتقال للنظام</button>
                        </form>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function toggleWarning() {
            const checkbox = document.getElementById('reset_db');
            const warning = document.getElementById('resetWarning');

            if (checkbox && warning) {
                warning.style.display = checkbox.checked ? 'block' : 'none';
            }
        }

        // تأكيد إضافي عند إرسال النموذج
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const resetCheckbox = document.getElementById('reset_db');

            if (form && resetCheckbox) {
                form.addEventListener('submit', function(e) {
                    if (resetCheckbox.checked) {
                        if (!confirm('هل أنت متأكد من حذف جميع البيانات الموجودة؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                            e.preventDefault();
                            return false;
                        }
                    }
                });
            }
        });
    </script>
</body>
</html>
