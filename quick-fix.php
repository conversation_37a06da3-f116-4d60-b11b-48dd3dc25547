<?php
/**
 * إصلاح سريع للمشاكل الشائعة
 * Quick Fix for Common Issues
 */

echo "<h1>🔧 إصلاح سريع للنظام</h1>";

try {
    require_once 'config/config.php';
    
    echo "<h2>1. تحديث تواريخ الاشتراك:</h2>";
    
    // تحديث تواريخ انتهاء الاشتراك
    $sql = "UPDATE companies SET 
            subscription_expires = '2025-12-31',
            status = 'active'";
    
    $database->query($sql);
    echo "✅ تم تحديث جميع تواريخ الاشتراك إلى 2025-12-31<br>";
    
    echo "<h2>2. التحقق من المستخدمين:</h2>";
    
    // التحقق من وجود المستخدمين التجريبيين
    $users = $database->fetchAll("SELECT username, email, role FROM users WHERE email IN ('<EMAIL>', '<EMAIL>')");
    
    if (empty($users)) {
        echo "❌ لا توجد مستخدمين تجريبيين. إنشاء مستخدمين جدد...<br>";
        
        // إنشاء مستخدمين تجريبيين
        $password_hash = password_hash('password', PASSWORD_DEFAULT);
        
        $database->query("INSERT INTO users (company_id, username, email, password, full_name, phone, role) VALUES 
                         (1, 'admin', '<EMAIL>', ?, 'مدير النظام', '+966501234567', 'admin')", 
                         [$password_hash]);
        
        $database->query("INSERT INTO users (company_id, username, email, password, full_name, phone, role) VALUES 
                         (1, 'agent1', '<EMAIL>', ?, 'أحمد محمد', '+966501234568', 'agent')", 
                         [$password_hash]);
        
        echo "✅ تم إنشاء المستخدمين التجريبيين<br>";
    } else {
        echo "✅ المستخدمون التجريبيون موجودون:<br>";
        foreach ($users as $user) {
            echo "- {$user['username']} ({$user['email']}) - {$user['role']}<br>";
        }
    }
    
    echo "<h2>3. التحقق من الشركات:</h2>";
    
    $companies = $database->fetchAll("SELECT name, subscription_plan, subscription_expires, status FROM companies");
    
    if (empty($companies)) {
        echo "❌ لا توجد شركات. إنشاء شركة تجريبية...<br>";
        
        $database->query("INSERT INTO companies (name, email, phone, address, subscription_plan, subscription_expires, status) VALUES 
                         ('شركة العقارات المتميزة', '<EMAIL>', '+966501234567', 'الرياض، المملكة العربية السعودية', 'premium', '2025-12-31', 'active')");
        
        echo "✅ تم إنشاء شركة تجريبية<br>";
    } else {
        echo "✅ الشركات الموجودة:<br>";
        foreach ($companies as $company) {
            $status_color = $company['status'] === 'active' ? 'green' : 'red';
            echo "- {$company['name']} ({$company['subscription_plan']}) - انتهاء: {$company['subscription_expires']} - <span style='color: {$status_color};'>{$company['status']}</span><br>";
        }
    }
    
    echo "<h2>4. إنشاء مجلدات الرفع:</h2>";
    
    $upload_dirs = ['uploads', 'uploads/properties', 'uploads/avatars'];
    
    foreach ($upload_dirs as $dir) {
        if (!file_exists($dir)) {
            if (mkdir($dir, 0755, true)) {
                echo "✅ تم إنشاء مجلد: {$dir}<br>";
            } else {
                echo "❌ فشل في إنشاء مجلد: {$dir}<br>";
            }
        } else {
            echo "✅ مجلد موجود: {$dir}<br>";
        }
    }
    
    echo "<h2>5. إنشاء ملف التثبيت:</h2>";
    
    if (!file_exists('.installed')) {
        file_put_contents('.installed', date('Y-m-d H:i:s') . " - Fixed by quick-fix.php");
        echo "✅ تم إنشاء ملف التثبيت<br>";
    } else {
        echo "✅ ملف التثبيت موجود<br>";
    }
    
    echo "<h2>✅ تم الانتهاء من الإصلاح!</h2>";
    echo "<p><strong>بيانات تسجيل الدخول:</strong></p>";
    echo "<ul>";
    echo "<li>البريد: <EMAIL></li>";
    echo "<li>كلمة المرور: password</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ خطأ في الإصلاح:</h2>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>تأكد من:</strong></p>";
    echo "<ul>";
    echo "<li>تشغيل خدمة MySQL</li>";
    echo "<li>صحة إعدادات قاعدة البيانات</li>";
    echo "<li>وجود قاعدة البيانات</li>";
    echo "</ul>";
}

echo "<br><div style='margin-top: 20px;'>";
echo "<a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>العودة للنظام</a>";
echo "<a href='test-connection.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>اختبار الاتصال</a>";
echo "<a href='install.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إعادة التثبيت</a>";
echo "</div>";
?>
