<?php
/**
 * الشريط الجانبي للمستخدم
 * User Sidebar
 */

$current_page = basename($_SERVER['PHP_SELF'], '.php');
$current_dir = basename(dirname($_SERVER['PHP_SELF']));

// تحديد الصفحة النشطة
function isActive($page, $dir = '') {
    global $current_page, $current_dir;
    
    if ($dir) {
        return $current_dir === $dir ? 'active' : '';
    }
    
    return $current_page === $page ? 'active' : '';
}
?>

<div class="col-md-3 col-lg-2 sidebar">
    <div class="d-flex flex-column h-100">
        <!-- Logo -->
        <div class="text-center mb-4">
            <h4 class="text-white">
                <i class="bi bi-building me-2"></i>
                <?php echo SITE_NAME; ?>
            </h4>
            <small class="text-white-50">نظام إدارة العقارات</small>
        </div>
        
        <!-- User Info -->
        <div class="text-center mb-4 p-3" style="background: rgba(255,255,255,0.1); border-radius: 10px;">
            <div class="mb-2">
                <?php if (!empty($user['avatar'])): ?>
                    <img src="../uploads/avatars/<?php echo $user['avatar']; ?>" 
                         alt="الصورة الشخصية" 
                         class="rounded-circle" 
                         width="50" height="50">
                <?php else: ?>
                    <div class="bg-white rounded-circle d-inline-flex align-items-center justify-content-center" 
                         style="width: 50px; height: 50px;">
                        <i class="bi bi-person text-primary fs-4"></i>
                    </div>
                <?php endif; ?>
            </div>
            <div class="text-white small">
                <strong><?php echo htmlspecialchars($user['full_name']); ?></strong>
                <br>
                <span class="text-white-50"><?php echo USER_ROLES[$user['role']]; ?></span>
            </div>
        </div>
        
        <!-- Navigation Menu -->
        <nav class="nav flex-column flex-grow-1">
            <a href="../user/dashboard.php" class="nav-link <?php echo isActive('dashboard'); ?>">
                <i class="bi bi-speedometer2"></i>
                لوحة التحكم
            </a>
            
            <!-- العقارات -->
            <div class="nav-item">
                <a href="#" class="nav-link" data-bs-toggle="collapse" data-bs-target="#propertiesMenu">
                    <i class="bi bi-building"></i>
                    العقارات
                    <i class="bi bi-chevron-down ms-auto"></i>
                </a>
                <div class="collapse <?php echo isActive('', 'properties') ? 'show' : ''; ?>" id="propertiesMenu">
                    <div class="nav flex-column ms-3">
                        <a href="../user/properties/" class="nav-link small">
                            <i class="bi bi-list"></i>
                            جميع العقارات
                        </a>
                        <a href="../user/properties/add.php" class="nav-link small">
                            <i class="bi bi-plus-circle"></i>
                            إضافة عقار
                        </a>
                        <a href="../user/properties/search.php" class="nav-link small">
                            <i class="bi bi-search"></i>
                            البحث المتقدم
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- العملاء -->
            <div class="nav-item">
                <a href="#" class="nav-link" data-bs-toggle="collapse" data-bs-target="#clientsMenu">
                    <i class="bi bi-people"></i>
                    العملاء
                    <i class="bi bi-chevron-down ms-auto"></i>
                </a>
                <div class="collapse <?php echo isActive('', 'clients') ? 'show' : ''; ?>" id="clientsMenu">
                    <div class="nav flex-column ms-3">
                        <a href="../user/clients/" class="nav-link small">
                            <i class="bi bi-list"></i>
                            جميع العملاء
                        </a>
                        <a href="../user/clients/add.php" class="nav-link small">
                            <i class="bi bi-person-plus"></i>
                            إضافة عميل
                        </a>
                        <a href="../user/clients/search.php" class="nav-link small">
                            <i class="bi bi-search"></i>
                            البحث في العملاء
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- المعاملات -->
            <div class="nav-item">
                <a href="#" class="nav-link" data-bs-toggle="collapse" data-bs-target="#transactionsMenu">
                    <i class="bi bi-receipt"></i>
                    المعاملات
                    <i class="bi bi-chevron-down ms-auto"></i>
                </a>
                <div class="collapse <?php echo isActive('', 'transactions') ? 'show' : ''; ?>" id="transactionsMenu">
                    <div class="nav flex-column ms-3">
                        <a href="../user/transactions/" class="nav-link small">
                            <i class="bi bi-list"></i>
                            جميع المعاملات
                        </a>
                        <a href="../user/transactions/add.php" class="nav-link small">
                            <i class="bi bi-plus-circle"></i>
                            معاملة جديدة
                        </a>
                        <a href="../user/transactions/pending.php" class="nav-link small">
                            <i class="bi bi-clock"></i>
                            المعاملات المعلقة
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- التقارير -->
            <div class="nav-item">
                <a href="#" class="nav-link" data-bs-toggle="collapse" data-bs-target="#reportsMenu">
                    <i class="bi bi-graph-up"></i>
                    التقارير
                    <i class="bi bi-chevron-down ms-auto"></i>
                </a>
                <div class="collapse <?php echo isActive('', 'reports') ? 'show' : ''; ?>" id="reportsMenu">
                    <div class="nav flex-column ms-3">
                        <a href="../user/reports/sales.php" class="nav-link small">
                            <i class="bi bi-bar-chart"></i>
                            تقرير المبيعات
                        </a>
                        <a href="../user/reports/commissions.php" class="nav-link small">
                            <i class="bi bi-cash-coin"></i>
                            تقرير العمولات
                        </a>
                        <a href="../user/reports/properties.php" class="nav-link small">
                            <i class="bi bi-building"></i>
                            تقرير العقارات
                        </a>
                        <a href="../user/reports/clients.php" class="nav-link small">
                            <i class="bi bi-people"></i>
                            تقرير العملاء
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- الإعدادات -->
            <?php if (hasPermission('admin', $user['role'])): ?>
            <div class="nav-item">
                <a href="#" class="nav-link" data-bs-toggle="collapse" data-bs-target="#settingsMenu">
                    <i class="bi bi-gear"></i>
                    الإعدادات
                    <i class="bi bi-chevron-down ms-auto"></i>
                </a>
                <div class="collapse <?php echo isActive('', 'settings') ? 'show' : ''; ?>" id="settingsMenu">
                    <div class="nav flex-column ms-3">
                        <a href="../user/settings/company.php" class="nav-link small">
                            <i class="bi bi-building"></i>
                            إعدادات الشركة
                        </a>
                        <a href="../user/settings/users.php" class="nav-link small">
                            <i class="bi bi-people"></i>
                            إدارة المستخدمين
                        </a>
                        <a href="../user/settings/subscription.php" class="nav-link small">
                            <i class="bi bi-credit-card"></i>
                            الاشتراك
                        </a>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- الملف الشخصي -->
            <a href="../user/profile.php" class="nav-link <?php echo isActive('profile'); ?>">
                <i class="bi bi-person-circle"></i>
                الملف الشخصي
            </a>
            
            <!-- المساعدة -->
            <a href="../user/help.php" class="nav-link <?php echo isActive('help'); ?>">
                <i class="bi bi-question-circle"></i>
                المساعدة
            </a>
        </nav>
        
        <!-- Footer -->
        <div class="mt-auto">
            <div class="nav flex-column">
                <a href="../logout.php" class="nav-link text-warning" onclick="return confirm('هل أنت متأكد من تسجيل الخروج؟')">
                    <i class="bi bi-box-arrow-right"></i>
                    تسجيل الخروج
                </a>
            </div>
            
            <!-- Subscription Info -->
            <div class="text-center mt-3 p-2" style="background: rgba(255,255,255,0.1); border-radius: 8px;">
                <small class="text-white-50">
                    <i class="bi bi-shield-check me-1"></i>
                    خطة <?php echo SUBSCRIPTION_PLANS[$user['subscription_plan']]['name']; ?>
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Mobile Menu Toggle -->
<div class="d-md-none">
    <button class="btn btn-primary position-fixed" 
            style="top: 20px; right: 20px; z-index: 1001;"
            type="button" 
            data-bs-toggle="offcanvas" 
            data-bs-target="#mobileSidebar">
        <i class="bi bi-list"></i>
    </button>
</div>

<!-- Mobile Sidebar -->
<div class="offcanvas offcanvas-end d-md-none" tabindex="-1" id="mobileSidebar">
    <div class="offcanvas-header bg-primary text-white">
        <h5 class="offcanvas-title">
            <i class="bi bi-building me-2"></i>
            <?php echo SITE_NAME; ?>
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="offcanvas"></button>
    </div>
    <div class="offcanvas-body p-0">
        <!-- نسخة مبسطة من القائمة للجوال -->
        <div class="nav flex-column">
            <a href="../user/dashboard.php" class="nav-link">
                <i class="bi bi-speedometer2 me-2"></i>
                لوحة التحكم
            </a>
            <a href="../user/properties/" class="nav-link">
                <i class="bi bi-building me-2"></i>
                العقارات
            </a>
            <a href="../user/clients/" class="nav-link">
                <i class="bi bi-people me-2"></i>
                العملاء
            </a>
            <a href="../user/transactions/" class="nav-link">
                <i class="bi bi-receipt me-2"></i>
                المعاملات
            </a>
            <a href="../user/reports/" class="nav-link">
                <i class="bi bi-graph-up me-2"></i>
                التقارير
            </a>
            <a href="../user/profile.php" class="nav-link">
                <i class="bi bi-person-circle me-2"></i>
                الملف الشخصي
            </a>
            <hr>
            <a href="../logout.php" class="nav-link text-danger">
                <i class="bi bi-box-arrow-right me-2"></i>
                تسجيل الخروج
            </a>
        </div>
    </div>
</div>

<style>
.nav-link {
    position: relative;
    transition: all 0.3s ease;
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white !important;
    border-radius: 8px;
}

.nav-link.active::before {
    content: '';
    position: absolute;
    right: -15px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    background: white;
    border-radius: 2px;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(-5px);
}

.collapse .nav-link {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}

.collapse .nav-link:hover {
    background: rgba(255, 255, 255, 0.05);
    transform: translateX(-3px);
}

@media (max-width: 768px) {
    .sidebar {
        display: none;
    }
}
</style>

<script>
// حفظ حالة القوائم المنسدلة
document.addEventListener('DOMContentLoaded', function() {
    // استرجاع حالة القوائم المحفوظة
    const savedStates = JSON.parse(localStorage.getItem('sidebarStates') || '{}');
    
    Object.keys(savedStates).forEach(menuId => {
        const menu = document.getElementById(menuId);
        if (menu && savedStates[menuId]) {
            menu.classList.add('show');
        }
    });
    
    // حفظ حالة القوائم عند التغيير
    document.querySelectorAll('[data-bs-toggle="collapse"]').forEach(toggle => {
        toggle.addEventListener('click', function() {
            const targetId = this.getAttribute('data-bs-target').substring(1);
            const target = document.getElementById(targetId);
            
            setTimeout(() => {
                const states = JSON.parse(localStorage.getItem('sidebarStates') || '{}');
                states[targetId] = target.classList.contains('show');
                localStorage.setItem('sidebarStates', JSON.stringify(states));
            }, 350); // انتظار انتهاء الحركة
        });
    });
});
</script>
